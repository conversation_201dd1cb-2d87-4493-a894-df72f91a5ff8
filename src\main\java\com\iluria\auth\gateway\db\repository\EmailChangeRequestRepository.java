package com.iluria.auth.gateway.db.repository;

import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.gateway.db.entity.EmailChangeRequestDBEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmailChangeRequestRepository extends JpaRepository<EmailChangeRequestDBEntity, byte[]> {
    
    Optional<EmailChangeRequestDBEntity> findByChangeToken(String changeToken);
    
    @Query("SELECT e FROM EmailChangeRequestDBEntity e WHERE e.userId = :userId AND e.status = :status")
    Optional<EmailChangeRequestDBEntity> findByUserIdAndStatus(@Param("userId") byte[] userId, @Param("status") EmailChangeStatus status);
    
    @Modifying
    @Query("UPDATE EmailChangeRequestDBEntity e SET e.status = :expiredStatus WHERE e.userId = :userId AND e.status = :pendingStatus")
    void invalidateOldRequests(@Param("userId") byte[] userId, @Param("expiredStatus") EmailChangeStatus expiredStatus, @Param("pendingStatus") EmailChangeStatus pendingStatus);
    
    boolean existsByNewEmailAndStatus(String newEmail, EmailChangeStatus status);
}