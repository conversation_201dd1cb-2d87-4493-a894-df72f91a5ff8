package com.iluria.auth.usecase;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.iluria.auth.domain.User;
import com.iluria.auth.entrypoint.controller.dto.ValidateCurrentPasswordDTO;
import com.iluria.auth.gateway.UserGateway;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ValidateCurrentPasswordUseCase {

    private final UserGateway userGateway;
    private final PasswordEncoder passwordEncoder;

    public ValidationResult validateCurrentPassword(ValidateCurrentPasswordDTO request) {
        try {
            User user = userGateway.findByEmail(request.email());
            
            if (user == null) {
                return new ValidationResult(false, "Usuário não encontrado");
            }
            
            boolean isValidPassword = passwordEncoder.matches(request.currentPassword(), user.getPassword());
            
            if (isValidPassword) {
                return new ValidationResult(true, "Senha válida");
            } else {
                return new ValidationResult(false, "Senha atual incorreta");
            }
            
        } catch (Exception e) {
            return new ValidationResult(false, "Erro ao validar senha");
        }
    }

    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        
        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public boolean isValid() { 
            return valid; 
        }
        
        public String getMessage() { 
            return message; 
        }
    }
}