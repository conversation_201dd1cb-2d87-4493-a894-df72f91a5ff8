package com.iluria.stores.store.usecase.theme;

import com.iluria.commons.domain.FileManagerEnum;
import com.iluria.commons.usecase.fileManager.CreateFileUseCase;
import com.iluria.commons.usecase.fileManager.RegisterFileManagerChangeUseCase;
import com.iluria.commons.gateway.FileManagerGateway;
import com.iluria.commons.domain.FileManager;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.domain.ThemeBackup;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeBackupGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 🚀 UseCase para sincronizar arquivos de backup do S3 para o file-manager
 * Resolve o problema de temas não aparecerem no file-manager após troca
 */
@Service
@RequiredArgsConstructor
public class SyncBackupToFileManagerUseCase {

    private final ThemeBackupGateway themeBackupGateway;
    private final CreateFileUseCase createFileUseCase;
    private final RegisterFileManagerChangeUseCase registerFileManagerChangeUseCase;
    private final FileManagerGateway fileManagerGateway;
    private final CleanupOrphanedFilesUseCase cleanupOrphanedFilesUseCase;
    private final S3Client s3Client;
    
    private static final String S3_BUCKET_NAME = "iluria-bucket-dev";

    /**
     * Sincroniza arquivos de um backup específico para o file-manager
     */
    @Transactional
    public SyncResult execute(ULID backupId, ULID storeId) {
        try {
            // Buscar informações do backup
            ThemeBackup backup = themeBackupGateway.findById(backupId);
            if (backup == null) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }
            
            if (!backup.getStoreId().equals(storeId)) {
                throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
            }
            
            // Baixar backup do S3
            Map<String, String> backupFiles = downloadBackupFromS3(backup);
            
            if (backupFiles.isEmpty()) {
                return SyncResult.builder()
                        .success(false)
                        .filesProcessed(0)
                        .message("Backup vazio ou inválido")
                        .build();
            }
            
            // PRIMEIRO: Limpar arquivos existentes no file-manager para evitar conflitos
            cleanupOrphanedFilesUseCase.clearAllHtmlFiles(storeId, FileManagerEnum.DEVELOP);
            
            // Sincronizar arquivos para file-manager
            int syncedFiles = syncFilesToFileManager(backupFiles, storeId);
            
            // Registrar mudança no file-manager para trigger DevEnvFilter
            registerFileManagerChangeUseCase.execute(storeId, FileManagerEnum.DEVELOP);
            
            return SyncResult.builder()
                    .success(true)
                    .filesProcessed(syncedFiles)
                    .message("Backup sincronizado com sucesso")
                    .backupId(backupId)
                    .build();
            
        } catch (Exception e) {
            return SyncResult.builder()
                    .success(false)
                    .filesProcessed(0)
                    .message("Erro: " + e.getMessage())
                    .backupId(backupId)
                    .build();
        }
    }

    /**
     * Sincroniza o último backup disponível de um tema para o file-manager
     */
    @Transactional
    public SyncResult executeLatestBackup(ULID themeId, ULID storeId) {
        try {
            // Buscar último backup do tema
            List<ThemeBackup> backups = themeBackupGateway.findLatestBackupsByThemeId(themeId, storeId, 1);
            
            if (backups.isEmpty()) {
                return SyncResult.builder()
                        .success(false)
                        .filesProcessed(0)
                        .message("Nenhum backup encontrado para o tema")
                        .build();
            }
            
            ThemeBackup latestBackup = backups.get(0);
            
            return execute(latestBackup.getId(), storeId);
            
        } catch (Exception e) {
            return SyncResult.builder()
                    .success(false)
                    .filesProcessed(0)
                    .message("Erro ao buscar último backup: " + e.getMessage())
                    .build();
        }
    }

    /**
     * Baixa arquivos do backup no S3 e retorna mapa fileName -> content
     */
    private Map<String, String> downloadBackupFromS3(ThemeBackup backup) throws IOException {
        Map<String, String> files = new HashMap<>();
        
        try {
            String s3Key = backup.getBackupS3Path();
            
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(S3_BUCKET_NAME)
                    .key(s3Key)
                    .build();
            
            ResponseInputStream<GetObjectResponse> s3Response = s3Client.getObject(getObjectRequest);
            byte[] zipContent = s3Response.readAllBytes();
            
            // Extrair arquivos do ZIP
            try (ZipInputStream zipIn = new ZipInputStream(new ByteArrayInputStream(zipContent))) {
                ZipEntry entry;
                while ((entry = zipIn.getNextEntry()) != null) {
                    if (!entry.isDirectory()) {
                        String fileName = entry.getName();
                        
                        // Ler conteúdo do arquivo
                        byte[] fileContent = zipIn.readAllBytes();
                        String content = new String(fileContent, StandardCharsets.UTF_8);
                        
                        files.put(fileName, content);
                    }
                    zipIn.closeEntry();
                }
            }
            
            return files;
            
        } catch (Exception e) {
            throw new IOException("Falha ao baixar backup do S3", e);
        }
    }

    /**
     * Sincroniza arquivos extraídos para o file-manager
     */
    private int syncFilesToFileManager(Map<String, String> files, ULID storeId) {
        int syncedCount = 0;
        
        for (Map.Entry<String, String> entry : files.entrySet()) {
            String fileName = entry.getKey();
            String content = entry.getValue();
            
            try {
                // Criar/atualizar arquivo no file-manager
                createFileUseCase.execute(fileName, content, null, "dev", storeId);
                syncedCount++;
            } catch (Exception e) {
                // Continua com próximo arquivo
            }
        }
        
        return syncedCount;
    }


    /**
     * Classe para resultado da sincronização
     */
    @lombok.Builder
    @lombok.Data
    public static class SyncResult {
        private boolean success;
        private int filesProcessed;
        private String message;
        private ULID backupId;
        
        public boolean isSuccess() {
            return success;
        }
        
        public int getFilesProcessed() {
            return filesProcessed;
        }
        
        public String getMessage() {
            return message;
        }
        
        public ULID getBackupId() {
            return backupId;
        }
    }
}