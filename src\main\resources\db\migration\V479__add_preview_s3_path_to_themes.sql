-- V436: Add preview_s3_path column to themes table
-- Adds support for storing S3 path for theme preview files

-- =============================================================================
-- 1. ADD PREVIEW_S3_PATH COLUMN TO THEMES TABLE
-- =============================================================================
ALTER TABLE themes 
ADD COLUMN preview_s3_path VARCHAR(500) NULL 
COMMENT 'S3 path for theme preview file (e.g., stores/{storeId}/themes/{themeId}/preview/index.html)';

-- =============================================================================
-- 2. ADD INDEX FOR PREVIEW_S3_PATH COLUMN
-- =============================================================================
CREATE INDEX idx_themes_preview_s3_path ON themes(preview_s3_path);

-- =============================================================================
-- 3. VALIDATION QUERY (COMMENTED - FOR REFERENCE)
-- =============================================================================
/*
-- Verify the column was added successfully
DESCRIBE themes;

-- Check if any themes already have preview paths
SELECT 
    name, 
    preview_s3_path IS NOT NULL as has_preview_path,
    preview_s3_path
FROM themes 
WHERE is_default = TRUE;
*/