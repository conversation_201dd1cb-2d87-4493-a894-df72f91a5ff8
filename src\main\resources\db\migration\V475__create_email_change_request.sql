CREATE TABLE email_change_request (
    id BINARY(16) NOT NULL PRIMARY KEY,
    user_id BINARY(16) NOT NULL,
    old_email VARCHAR(255) NOT NULL,
    new_email VARCHAR(255) NOT NULL,
    verification_code VARCHAR(6) NOT NULL,
    change_token VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    used_at DATETIME,
    status VARCHAR(20) NOT NULL,
    CONSTRAINT fk_email_change_user FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_change_token (change_token),
    INDEX idx_user_status (user_id, status),
    INDEX idx_new_email_status (new_email, status)
);