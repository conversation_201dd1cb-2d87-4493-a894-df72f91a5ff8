package com.iluria.stores.store.service;

import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.stores.store.exception.ThemeException;
import io.github.jaspeen.ulid.ULID;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Centralized error handling service for theme operations.
 * Tracks errors, provides recovery strategies, and manages error states.
 */
@Service
public class ThemeErrorHandler {
    
    private final ConcurrentHashMap<String, ErrorMetrics> errorMetrics = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, RecoveryState> recoveryStates = new ConcurrentHashMap<>();
    
    private static final int MAX_RETRIES = 3;
    private static final long ERROR_WINDOW_MINUTES = 5;
    private static final int ERROR_THRESHOLD = 5;
    
    /**
     * Handle theme operation error with context
     */
    public void handleError(ULID storeId, String operation, Exception error) {
        String key = getErrorKey(storeId, operation);
        ErrorMetrics metrics = errorMetrics.computeIfAbsent(key, k -> new ErrorMetrics());
        metrics.recordError(error);
        
        // Check if we need to trigger recovery
        if (metrics.shouldTriggerRecovery()) {
            initiateRecovery(storeId, operation);
        }
    }
    
    /**
     * Check if operation should be retried
     */
    public boolean shouldRetry(ULID storeId, String operation) {
        String key = getErrorKey(storeId, operation);
        ErrorMetrics metrics = errorMetrics.get(key);
        
        if (metrics == null) {
            return true;
        }
        
        return metrics.getRetryCount() < MAX_RETRIES && !isInRecoveryMode(storeId, operation);
    }
    
    /**
     * Record successful operation
     */
    public void recordSuccess(ULID storeId, String operation) {
        String key = getErrorKey(storeId, operation);
        errorMetrics.remove(key);
        recoveryStates.remove(key);
    }
    
    /**
     * Check if store is in recovery mode for operation
     */
    public boolean isInRecoveryMode(ULID storeId, String operation) {
        String key = getErrorKey(storeId, operation);
        RecoveryState state = recoveryStates.get(key);
        
        if (state == null) {
            return false;
        }
        
        // Recovery expires after 30 minutes
        if (state.getStartTime().isBefore(LocalDateTime.now().minusMinutes(30))) {
            recoveryStates.remove(key);
            return false;
        }
        
        return state.isActive();
    }
    
    /**
     * Get appropriate error message based on context
     */
    public StoreErrorMessageEnum getErrorMessage(String operation, Exception error) {
        if (error instanceof ThemeException) {
            // Since ThemeException was created with StoreErrorMessageEnum, we need to handle this differently
            // Return a generic error message for theme exceptions
            return StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED;
        }
        
        // Map operations to appropriate error messages
        switch (operation) {
            case "switch_theme":
            case "activate_theme":
                return StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED;
            case "upload_template":
                return StoreErrorMessageEnum.THEME_TEMPLATE_UPLOAD_FAILED;
            case "sync_template":
                return StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED;
            case "download_template":
                return StoreErrorMessageEnum.THEME_TEMPLATE_DOWNLOAD_FAILED;
            case "delete_template":
                return StoreErrorMessageEnum.THEME_TEMPLATE_DELETE_FAILED;
            case "validate_file":
                return StoreErrorMessageEnum.THEME_INVALID_FILE_NAME;
            default:
                return StoreErrorMessageEnum.THEME_TEMPLATE_SYNC_FAILED;
        }
    }
    
    /**
     * Initiate recovery for failed operations
     */
    private void initiateRecovery(ULID storeId, String operation) {
        String key = getErrorKey(storeId, operation);
        RecoveryState state = new RecoveryState();
        recoveryStates.put(key, state);
    }
    
    private String getErrorKey(ULID storeId, String operation) {
        return storeId.toString() + "_" + operation;
    }
    
    /**
     * Clean up old error metrics periodically
     */
    public void cleanupOldMetrics() {
        LocalDateTime cutoff = LocalDateTime.now().minusMinutes(ERROR_WINDOW_MINUTES * 2);
        
        errorMetrics.entrySet().removeIf(entry -> {
            ErrorMetrics metrics = entry.getValue();
            return metrics.getLastErrorTime().isBefore(cutoff);
        });
        
        recoveryStates.entrySet().removeIf(entry -> {
            RecoveryState state = entry.getValue();
            return state.getStartTime().isBefore(cutoff);
        });
    }
    
    /**
     * Inner class to track error metrics
     */
    private static class ErrorMetrics {
        private final AtomicInteger errorCount = new AtomicInteger(0);
        private final AtomicInteger retryCount = new AtomicInteger(0);
        private volatile LocalDateTime lastErrorTime = LocalDateTime.now();
        private volatile Exception lastError;
        
        void recordError(Exception error) {
            errorCount.incrementAndGet();
            retryCount.incrementAndGet();
            lastErrorTime = LocalDateTime.now();
            lastError = error;
        }
        
        boolean shouldTriggerRecovery() {
            // Check if errors are happening too frequently
            LocalDateTime windowStart = LocalDateTime.now().minusMinutes(ERROR_WINDOW_MINUTES);
            return lastErrorTime.isAfter(windowStart) && errorCount.get() >= ERROR_THRESHOLD;
        }
        
        int getRetryCount() {
            return retryCount.get();
        }
        
        LocalDateTime getLastErrorTime() {
            return lastErrorTime;
        }
    }
    
    /**
     * Inner class to track recovery state
     */
    private static class RecoveryState {
        private final LocalDateTime startTime = LocalDateTime.now();
        private volatile boolean active = true;
        
        boolean isActive() {
            return active;
        }
        
        LocalDateTime getStartTime() {
            return startTime;
        }
        
        void complete() {
            active = false;
        }
    }
}