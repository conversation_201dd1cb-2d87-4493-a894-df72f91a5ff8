package com.iluria.stores.store.service;

import com.iluria.auth.domain.User;
import com.iluria.auth.gateway.UserGateway;
import com.iluria.commons.domain.Permission;
import com.iluria.commons.notification.service.RoleBasedNotificationValidationService.UserStorePermissionProvider;
import com.iluria.stores.store.domain.StoreRole;
import com.iluria.stores.store.gateway.StoreRoleGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Set;

/**
 * Implementação do provider de permissões que busca permissões do usuário
 * na loja através da tabela store_roles.permissions_json.
 * 
 * Fluxo: User.stores → StoreRole → permissions_json
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserStorePermissionProviderImpl implements UserStorePermissionProvider {
    
    private final UserGateway userGateway;
    private final StoreRoleGateway storeRoleGateway;
    
    @Override
    public Set<Permission> getUserPermissionsInStore(ULID userId, ULID storeId) {
        try {
            // 1. Buscar o usuário
            User user = userGateway.findById(userId);
            if (user == null) {
                log.warn("User {} not found", userId);
                return Set.of();
            }
            
            // 2. Verificar se o usuário tem acesso à loja
            if (!user.hasAccessToStore(storeId)) {
                log.debug("User {} has no access to store {}", userId, storeId);
                return Set.of();
            }
            
            // 3. Buscar o roleId do usuário na loja
            String roleIdStr = user.getStoreRoleId(storeId);
            if (roleIdStr == null) {
                log.warn("User {} has no role defined for store {}", userId, storeId);
                return Set.of();
            }
            
            // 4. Converter para ULID (suporta formato legado)
            ULID roleId;
            try {
                roleId = ULID.fromString(roleIdStr);
            } catch (IllegalArgumentException e) {
                // Formato legado - role armazenado como string nome
                log.debug("User {} has legacy role format '{}' in store {}", userId, roleIdStr, storeId);
                return getLegacyRolePermissions(roleIdStr);
            }
            
            // 5. Buscar o StoreRole para obter as permissões do JSON
            Optional<StoreRole> storeRoleOpt = storeRoleGateway.findById(roleId);
            if (storeRoleOpt.isEmpty()) {
                log.warn("StoreRole {} not found for user {} in store {}", roleId, userId, storeId);
                return Set.of();
            }
            
            StoreRole storeRole = storeRoleOpt.get();
            
            // 6. Retornar as permissões do role (já parseadas do JSON pelo mapper)
            Set<Permission> permissions = storeRole.getPermissions();
            
            log.debug("User {} has {} permissions in store {} via role '{}': {}", 
                     userId, 
                     permissions.size(), 
                     storeId, 
                     storeRole.getName(),
                     permissions.stream().map(Permission::getCode).toList());
            
            return permissions != null ? permissions : Set.of();
            
        } catch (Exception e) {
            log.error("Error getting user permissions for user {} in store {}: {}", 
                     userId, storeId, e.getMessage(), e);
            return Set.of();
        }
    }
    
    /**
     * Mapeia roles legados (armazenados como string) para permissões padrão.
     * Este método oferece compatibilidade com o sistema antigo.
     */
    private Set<Permission> getLegacyRolePermissions(String legacyRoleName) {
        return switch (legacyRoleName.toLowerCase()) {
            case "admin", "administrator", "owner" -> Set.of(
                // Admin tem todas as permissões de notificação
                Permission.NOTIFICATION_NEW_SALES,
                Permission.NOTIFICATION_PRODUCT_REVIEWS,
                Permission.NOTIFICATION_PRODUCT_QUESTIONS,
                Permission.NOTIFICATION_NEWSLETTER_SUBSCRIPTIONS,
                Permission.NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS
            );
            
            case "manager", "gerente" -> Set.of(
                // Manager tem a maioria das permissões exceto algumas específicas
                Permission.NOTIFICATION_NEW_SALES,
                Permission.NOTIFICATION_PRODUCT_REVIEWS,
                Permission.NOTIFICATION_PRODUCT_QUESTIONS,
                Permission.NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS
            );
            
            case "employee", "funcionario", "staff" -> Set.of(
                // Funcionário tem permissões básicas
                Permission.NOTIFICATION_PRODUCT_REVIEWS,
                Permission.NOTIFICATION_PRODUCT_QUESTIONS
            );
            
            case "viewer", "readonly" -> Set.of(
                // Visualizador não recebe notificações por padrão
            );
            
            default -> {
                log.warn("Unknown legacy role name: {}", legacyRoleName);
                yield Set.of(); // Sem permissões para roles desconhecidos
            }
        };
    }
}