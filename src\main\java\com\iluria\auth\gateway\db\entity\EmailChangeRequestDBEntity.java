package com.iluria.auth.gateway.db.entity;

import com.iluria.auth.domain.EmailChangeStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "email_change_request")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailChangeRequestDBEntity {
    
    @Id
    @Column(name = "id", columnDefinition = "BINARY(16)")
    private byte[] id;
    
    @Column(name = "user_id", columnDefinition = "BINARY(16)", nullable = false)
    private byte[] userId;
    
    @Column(name = "old_email", nullable = false)
    private String oldEmail;
    
    @Column(name = "new_email", nullable = false)
    private String newEmail;
    
    @Column(name = "verification_code", nullable = false)
    private String verificationCode;
    
    @Column(name = "change_token", nullable = false, unique = true)
    private String changeToken;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    @Column(name = "used_at")
    private LocalDateTime usedAt;
    
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private EmailChangeStatus status;
}