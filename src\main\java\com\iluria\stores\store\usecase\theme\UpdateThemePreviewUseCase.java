package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.exception.ThemeException;
import com.iluria.stores.store.gateway.ThemeGateway;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
public class UpdateThemePreviewUseCase {

    private final ThemeGateway themeGateway;
    private final S3Client s3Client;
    
    private static final String S3_BUCKET_NAME = "iluria-bucket-dev";

    @Transactional
    public Theme execute(ULID themeId, ULID storeId, String indexHtmlContent) {
        validateInputs(themeId, storeId, indexHtmlContent);
        
        Theme theme = findAndValidateTheme(themeId, storeId);
        
        // Generate preview S3 path
        String previewS3Path = generatePreviewS3Path(storeId, themeId);
        
        // Upload preview to S3
        uploadPreviewToS3(previewS3Path, indexHtmlContent);
        
        // Update theme with preview path
        theme.updatePreviewS3Path(previewS3Path);
        
        return themeGateway.save(theme);
    }

    private void validateInputs(ULID themeId, ULID storeId, String indexHtmlContent) {
        if (themeId == null) {
            throw new IllegalArgumentException("Theme ID cannot be null");
        }
        
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }
        
        if (indexHtmlContent == null || indexHtmlContent.trim().isEmpty()) {
            throw new IllegalArgumentException("Index HTML content cannot be null or empty");
        }
    }

    private Theme findAndValidateTheme(ULID themeId, ULID storeId) {
        Theme theme = themeGateway.findById(themeId);
        
        if (theme == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        // Validate theme belongs to store (for non-default themes)
        if (!Boolean.TRUE.equals(theme.getIsDefault()) && 
            (theme.getStoreId() == null || !theme.getStoreId().equals(storeId))) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_NOT_FOUND);
        }
        
        return theme;
    }

    private String generatePreviewS3Path(ULID storeId, ULID themeId) {
        return String.format("stores/%s/themes/%s/preview/index.html", storeId.toString(), themeId.toString());
    }

    private void uploadPreviewToS3(String s3Path, String indexHtmlContent) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(S3_BUCKET_NAME)
                    .key(s3Path)
                    .contentType("text/html")
                    .build();

            RequestBody requestBody = RequestBody.fromString(indexHtmlContent, StandardCharsets.UTF_8);
            
            s3Client.putObject(putObjectRequest, requestBody);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload theme preview to S3", e);
        }
    }
}