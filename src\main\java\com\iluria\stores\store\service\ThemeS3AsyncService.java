package com.iluria.stores.store.service;

import com.iluria.stores.store.util.FileSecurityValidator;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.model.*;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Asynchronous S3 service for theme operations.
 * Handles uploads and downloads with non-blocking I/O.
 */
@Service
@RequiredArgsConstructor
public class ThemeS3AsyncService {
    
    private final S3AsyncClient s3AsyncClient;
    private final ThemeErrorHandler themeErrorHandler;
    
    @Value("${aws.s3.bucket-name}")
    private String bucketName;
    
    // Track ongoing operations for monitoring
    private final ConcurrentHashMap<String, CompletableFuture<?>> ongoingOperations = new ConcurrentHashMap<>();
    
    /**
     * Upload multiple files to S3 asynchronously
     */
    @Async
    public CompletableFuture<Map<String, Boolean>> uploadFilesAsync(
            ULID storeId,
            String basePath,
            Map<String, String> files) {
        
        Map<String, CompletableFuture<Boolean>> uploadFutures = new HashMap<>();
        
        for (Map.Entry<String, String> entry : files.entrySet()) {
            String fileName = entry.getKey();
            String content = entry.getValue();
            
            try {
                // Validate file name for security
                String sanitizedName = FileSecurityValidator.validateAndSanitizeFileName(fileName);
                String s3Key = FileSecurityValidator.validateS3Path(basePath + "/" + sanitizedName);
                
                CompletableFuture<Boolean> uploadFuture = uploadSingleFileAsync(
                    storeId, s3Key, content, getContentType(fileName)
                );
                
                uploadFutures.put(fileName, uploadFuture);
                
            } catch (Exception e) {
                themeErrorHandler.handleError(storeId, "async_upload", e);
                uploadFutures.put(fileName, CompletableFuture.completedFuture(false));
            }
        }
        
        // Combine all futures into a single result
        return CompletableFuture.allOf(
            uploadFutures.values().toArray(new CompletableFuture[0])
        ).thenApply(v -> {
            Map<String, Boolean> results = new HashMap<>();
            uploadFutures.forEach((fileName, future) -> {
                try {
                    results.put(fileName, future.get());
                } catch (Exception e) {
                    results.put(fileName, false);
                }
            });
            return results;
        });
    }
    
    /**
     * Upload single file to S3 asynchronously
     */
    private CompletableFuture<Boolean> uploadSingleFileAsync(
            ULID storeId,
            String s3Key,
            String content,
            String contentType) {
        
        String operationKey = storeId + "_upload_" + s3Key;
        
        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .contentType(contentType)
                .build();
        
        CompletableFuture<Boolean> future = s3AsyncClient.putObject(
            request,
            AsyncRequestBody.fromString(content, StandardCharsets.UTF_8)
        ).thenApply(response -> {
            ongoingOperations.remove(operationKey);
            return true;
        }).exceptionally(ex -> {
            themeErrorHandler.handleError(storeId, "async_upload_single", ex instanceof Exception ? (Exception) ex : new RuntimeException(ex));
            ongoingOperations.remove(operationKey);
            return false;
        });
        
        ongoingOperations.put(operationKey, future);
        return future;
    }
    
    /**
     * Download multiple files from S3 asynchronously
     */
    @Async
    public CompletableFuture<Map<String, String>> downloadFilesAsync(
            ULID storeId,
            String basePath,
            Set<String> fileNames) {
        
        Map<String, CompletableFuture<String>> downloadFutures = new HashMap<>();
        
        for (String fileName : fileNames) {
            try {
                String s3Key = FileSecurityValidator.validateS3Path(basePath + "/" + fileName);
                
                CompletableFuture<String> downloadFuture = downloadSingleFileAsync(
                    storeId, s3Key
                );
                
                downloadFutures.put(fileName, downloadFuture);
                
            } catch (Exception e) {
                themeErrorHandler.handleError(storeId, "async_download", e);
                downloadFutures.put(fileName, CompletableFuture.completedFuture(""));
            }
        }
        
        // Combine all futures into a single result
        return CompletableFuture.allOf(
            downloadFutures.values().toArray(new CompletableFuture[0])
        ).thenApply(v -> {
            Map<String, String> results = new HashMap<>();
            downloadFutures.forEach((fileName, future) -> {
                try {
                    String content = future.get();
                    if (content != null && !content.isEmpty()) {
                        results.put(fileName, content);
                    }
                } catch (Exception e) {
                    // Skip failed downloads
                }
            });
            return results;
        });
    }
    
    /**
     * Download single file from S3 asynchronously
     */
    private CompletableFuture<String> downloadSingleFileAsync(ULID storeId, String s3Key) {
        String operationKey = storeId + "_download_" + s3Key;
        
        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .build();
        
        CompletableFuture<String> future = s3AsyncClient.getObject(
            request,
            software.amazon.awssdk.core.async.AsyncResponseTransformer.toBytes()
        ).thenApply(response -> {
            ongoingOperations.remove(operationKey);
            return response.asUtf8String();
        }).exceptionally(ex -> {
            themeErrorHandler.handleError(storeId, "async_download_single", ex instanceof Exception ? (Exception) ex : new RuntimeException(ex));
            ongoingOperations.remove(operationKey);
            return "";
        });
        
        ongoingOperations.put(operationKey, future);
        return future;
    }
    
    /**
     * Delete multiple files from S3 asynchronously
     */
    @Async
    public CompletableFuture<Boolean> deleteFilesAsync(
            ULID storeId,
            String basePath,
            Set<String> fileNames) {
        
        if (fileNames.isEmpty()) {
            return CompletableFuture.completedFuture(true);
        }
        
        try {
            // Build delete request
            Delete delete = Delete.builder()
                    .objects(fileNames.stream()
                            .map(fileName -> {
                                try {
                                    String s3Key = FileSecurityValidator.validateS3Path(basePath + "/" + fileName);
                                    return ObjectIdentifier.builder().key(s3Key).build();
                                } catch (Exception e) {
                                    return null;
                                }
                            })
                            .filter(obj -> obj != null)
                            .toList())
                    .build();
            
            DeleteObjectsRequest request = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(delete)
                    .build();
            
            return s3AsyncClient.deleteObjects(request)
                    .thenApply(response -> true)
                    .exceptionally(ex -> {
                        themeErrorHandler.handleError(storeId, "async_delete", ex instanceof Exception ? (Exception) ex : new RuntimeException(ex));
                        return false;
                    });
                    
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "async_delete_prepare", e);
            return CompletableFuture.completedFuture(false);
        }
    }
    
    /**
     * Check if file exists in S3 asynchronously
     */
    @Async
    public CompletableFuture<Boolean> fileExistsAsync(ULID storeId, String s3Key) {
        try {
            String validatedKey = FileSecurityValidator.validateS3Path(s3Key);
            
            HeadObjectRequest request = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(validatedKey)
                    .build();
            
            return s3AsyncClient.headObject(request)
                    .thenApply(response -> true)
                    .exceptionally(ex -> {
                        if (ex.getCause() instanceof NoSuchKeyException) {
                            return false;
                        }
                        themeErrorHandler.handleError(storeId, "async_exists", ex instanceof Exception ? (Exception) ex : new RuntimeException(ex));
                        return false;
                    });
                    
        } catch (Exception e) {
            themeErrorHandler.handleError(storeId, "async_exists_prepare", e);
            return CompletableFuture.completedFuture(false);
        }
    }
    
    /**
     * Get operation status
     */
    public boolean hasOngoingOperations(ULID storeId) {
        String storePrefix = storeId.toString() + "_";
        return ongoingOperations.keySet().stream()
                .anyMatch(key -> key.startsWith(storePrefix));
    }
    
    /**
     * Wait for all operations to complete for a store
     */
    public CompletableFuture<Void> waitForStoreOperations(ULID storeId) {
        String storePrefix = storeId.toString() + "_";
        
        CompletableFuture<?>[] storeFutures = ongoingOperations.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(storePrefix))
                .map(Map.Entry::getValue)
                .toArray(CompletableFuture[]::new);
        
        if (storeFutures.length == 0) {
            return CompletableFuture.completedFuture(null);
        }
        
        return CompletableFuture.allOf(storeFutures);
    }
    
    private String getContentType(String fileName) {
        if (fileName.endsWith(".html")) {
            return "text/html";
        } else if (fileName.endsWith(".css")) {
            return "text/css";
        } else if (fileName.endsWith(".js")) {
            return "application/javascript";
        } else if (fileName.endsWith(".json")) {
            return "application/json";
        } else {
            return "text/plain";
        }
    }
}