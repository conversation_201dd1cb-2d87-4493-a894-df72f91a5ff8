package com.iluria.stores.store.usecase;

import com.iluria.auth.gateway.UserGateway;
import com.iluria.stores.store.domain.StoreRole;
import com.iluria.stores.store.gateway.StoreRoleGateway;
import io.github.jaspeen.ulid.ULID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DeleteStoreRoleUseCaseTest {

    @Mock
    private StoreRoleGateway storeRoleGateway;

    @Mock
    private UserGateway userGateway;

    private DeleteStoreRoleUseCase deleteStoreRoleUseCase;

    @BeforeEach
    void setUp() {
        deleteStoreRoleUseCase = new DeleteStoreRoleUseCase(storeRoleGateway, userGateway);
    }

    @Test
    void shouldDeleteRoleWhenValidAndNotInUse() {
        // Given
        ULID roleId = ULID.random();
        StoreRole role = StoreRole.builder()
                .id(roleId)
                .name("Custom Role")
                .isSystemRole(false)
                .build();

        when(storeRoleGateway.findById(roleId)).thenReturn(Optional.of(role));
        when(userGateway.existsUserWithRoleId(roleId)).thenReturn(false);

        // When
        deleteStoreRoleUseCase.execute(roleId);

        // Then
        verify(storeRoleGateway).deleteById(roleId);
    }

    @Test
    void shouldThrowExceptionWhenRoleNotFound() {
        // Given
        ULID roleId = ULID.random();
        when(storeRoleGateway.findById(roleId)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> deleteStoreRoleUseCase.execute(roleId)
        );
        assertEquals("Cargo não encontrado", exception.getMessage());
        verify(storeRoleGateway, never()).deleteById(any());
    }

    @Test
    void shouldThrowExceptionWhenRoleIsSystemRole() {
        // Given
        ULID roleId = ULID.random();
        StoreRole systemRole = StoreRole.builder()
                .id(roleId)
                .name("Owner")
                .isSystemRole(true)
                .build();

        when(storeRoleGateway.findById(roleId)).thenReturn(Optional.of(systemRole));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> deleteStoreRoleUseCase.execute(roleId)
        );
        assertEquals("Não é possível excluir cargos padrão do sistema", exception.getMessage());
        verify(storeRoleGateway, never()).deleteById(any());
        verify(userGateway, never()).existsUserWithRoleId(any());
    }

    @Test
    void shouldThrowExceptionWhenRoleIsInUseByTeamMembers() {
        // Given
        ULID roleId = ULID.random();
        StoreRole role = StoreRole.builder()
                .id(roleId)
                .name("Manager")
                .isSystemRole(false)
                .build();

        when(storeRoleGateway.findById(roleId)).thenReturn(Optional.of(role));
        when(userGateway.existsUserWithRoleId(roleId)).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> deleteStoreRoleUseCase.execute(roleId)
        );
        assertEquals("Não é possível excluir um cargo que está sendo usado por membros da equipe", exception.getMessage());
        verify(storeRoleGateway, never()).deleteById(any());
    }
}
