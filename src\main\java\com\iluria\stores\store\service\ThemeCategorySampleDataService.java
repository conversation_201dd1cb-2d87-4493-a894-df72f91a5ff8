package com.iluria.stores.store.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.iluria.stores.store.domain.ThemeCategory;
import com.iluria.stores.store.domain.ThemeCategoryReference;
import com.iluria.stores.store.gateway.ThemeCategoryGateway;
import com.iluria.stores.store.gateway.ThemeCategoryReferenceGateway;

import io.github.jaspeen.ulid.ULID;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;


@Service
@RequiredArgsConstructor
@Transactional
public class ThemeCategorySampleDataService {
    private final ThemeCategoryReferenceGateway themeCategoryReferenceGateway;
    private final ThemeCategoryGateway themeCategoryGateway;

    public void createSampleThemeCategories(ULID storeId) {
        List<ThemeCategoryReference> referenceCategories = themeCategoryReferenceGateway.findAll();
        if (referenceCategories.isEmpty()) {
            return;
        }

        for (ThemeCategoryReference ref : referenceCategories) {
            ThemeCategory category = ThemeCategory.builder()
                .id(ULID.random())
                .storeId(storeId)
                .name(ref.getName())
                .slug(ref.getSlug())
                .description(ref.getDescription())
                .color(ref.getColor())
                .sortOrder(ref.getSortOrder())
                .isActive(ref.getIsActive())
                .build();
            themeCategoryGateway.save(category);
        }
    }
}
