package com.iluria.stores.store.usecase.theme;

import com.iluria.stores.store.domain.Theme;
import com.iluria.stores.store.gateway.ThemeGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Use case para buscar apenas temas específicos da loja (exclui temas padrão)
 * Usado para exibição na galeria de temas
 */
@Service
@RequiredArgsConstructor
public class FindStoreSpecificThemesUseCase {

    private final ThemeGateway gateway;

    public List<Theme> execute(ULID storeId) {
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }

        return gateway.findStoreSpecificThemes(storeId);
    }

    public List<Theme> executeWithCategory(ULID storeId, ULID categoryId) {
        if (storeId == null) {
            throw new IllegalArgumentException("Store ID cannot be null");
        }

        if (categoryId == null) {
            return gateway.findStoreSpecificThemes(storeId);
        }

        return gateway.findByStoreIdAndCategoryId(storeId, categoryId)
                .stream()
                .filter(theme -> theme.getStoreId() != null && theme.getStoreId().equals(storeId))
                .collect(java.util.stream.Collectors.toList());
    }
}
