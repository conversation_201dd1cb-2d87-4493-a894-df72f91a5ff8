-- Update Proprietário role to ensure all notification permissions are included
-- This migration ensures that store owners can see all notifications
UPDATE store_roles 
SET permissions_json = JSON_ARRAY(
    'store.admin', 'store.settings', 'store.view',
    'product.create', 'product.edit', 'product.delete', 'product.view', 'product.category.manage',
    'order.view', 'order.edit', 'order.cancel', 'order.export', 'order.status.change',
    'customer.view', 'customer.edit', 'customer.delete', 'customer.export',
    'financial.view', 'financial.reports', 'financial.export',
    'promotion.create', 'promotion.edit', 'promotion.delete', 'promotion.view',
    'layout.edit', 'layout.publish', 'layout.template.manage',
    'file.upload', 'file.delete', 'file.manage',
    'analytics.view', 'reports.generate',
    'team.view', 'team.invite', 'team.edit',
    'payment.settings', 'payment.view', 'shipping.settings', 'shipping.view',
    -- Notification permissions (critical for store owners)
    'notification.newSales', 
    'notification.productReviews', 
    'notification.productQuestions',
    'notification.newsletterSubscriptions', 
    'notification.newCustomerRegistrations'
),
updated_at = NOW()
WHERE name = 'Proprietário' 
  AND is_active = true 
  AND is_system_role = true;

-- Log the update for verification
SELECT 
    sr.id,
    sr.store_id,
    sr.name,
    sr.permissions_json,
    sr.updated_at
FROM store_roles sr 
WHERE sr.name = 'Proprietário' 
  AND sr.is_active = true 
  AND sr.is_system_role = true
LIMIT 5; -- Limit output for readability