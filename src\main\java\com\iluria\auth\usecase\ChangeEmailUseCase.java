package com.iluria.auth.usecase;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.domain.User;
import com.iluria.auth.entrypoint.controller.dto.ChangeEmailDTO;
import com.iluria.auth.exceptions.InvalidMfaTokenException;
import com.iluria.auth.exceptions.PasswordRecoveryException;
import com.iluria.auth.gateway.EmailChangeRequestGateway;
import com.iluria.auth.gateway.UserGateway;
import com.iluria.auth.service.EmailService;
import com.iluria.auth.service.authentication.OtpService;
import com.iluria.exception.StoreErrorMessageEnum;
import com.iluria.auth.domain.MfaType;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChangeEmailUseCase {
    
    private final UserGateway userGateway;
    private final EmailChangeRequestGateway emailChangeRequestGateway;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final OtpService otpService;
    private final Random random = new Random();
    
    @Transactional
    public Map<String, Object> initiateEmailChange(ChangeEmailDTO request, ULID userId) {
        // Busca o usuário
        User user = userGateway.findById(userId);
        
        // Valida senha atual
        if (!passwordEncoder.matches(request.currentPassword(), user.getPassword())) {
            throw new PasswordRecoveryException(StoreErrorMessageEnum.INVALID_CURRENT_PASSWORD);
        }
        
        // Valida se o novo email já está em uso
        if (userGateway.verifyExistingUser(request.newEmail())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_ALREADY_EXISTS);
        }
        
        // Se usuário tem MFA habilitado e código MFA foi fornecido
        if (Boolean.TRUE.equals(user.getMfaEnabled()) && request.mfaCode() != null && !request.mfaCode().trim().isEmpty()) {
            // Busca solicitação pendente do usuário
            Optional<EmailChangeRequest> existingRequest = emailChangeRequestGateway
                    .findByUserIdAndStatus(userId, EmailChangeStatus.PENDING);
            
            if (existingRequest.isPresent()) {
                EmailChangeRequest changeRequest = existingRequest.get();
                
                // Verifica se é para o mesmo email
                if (!changeRequest.getNewEmail().equals(request.newEmail())) {
                    throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_MISMATCH);
                }
                
                // Valida código MFA
                validateMfaCode(user, request.mfaCode());
                
                // MFA validado, envia código de verificação existente para o novo email
                emailService.sendEmailChangeVerificationCode(changeRequest.getNewEmail(), changeRequest.getVerificationCode());
                
                return Map.of(
                        "success", true,
                        "requiresMfa", false,
                        "changeToken", changeRequest.getChangeToken(),
                        "message", "Código de verificação enviado para o novo email"
                );
            }
        }
        
        // Valida se já existe uma solicitação pendente para este email
        if (emailChangeRequestGateway.existsPendingRequestForEmail(request.newEmail())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_ALREADY_PENDING);
        }
        
        // Invalida solicitações antigas do usuário
        emailChangeRequestGateway.invalidateOldRequests(userId);
        
        // Gera código de verificação e token
        String verificationCode = generateVerificationCode();
        String changeToken = UUID.randomUUID().toString();
        
        // Cria nova solicitação
        EmailChangeRequest changeRequest = EmailChangeRequest.builder()
                .id(ULID.random())
                .userId(userId)
                .oldEmail(user.getEmail())
                .newEmail(request.newEmail())
                .verificationCode(verificationCode)
                .changeToken(changeToken)
                .createdAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusMinutes(15))
                .status(EmailChangeStatus.PENDING)
                .build();
        
        emailChangeRequestGateway.save(changeRequest);
        
        // Se usuário tem MFA habilitado
        if (Boolean.TRUE.equals(user.getMfaEnabled())) {
            // Se código MFA foi fornecido, valida
            if (request.mfaCode() != null && !request.mfaCode().trim().isEmpty()) {
                validateMfaCode(user, request.mfaCode());
                
                // MFA validado, envia código de verificação para o novo email
                emailService.sendEmailChangeVerificationCode(request.newEmail(), verificationCode);
                
                return Map.of(
                        "success", true,
                        "requiresMfa", false,
                        "changeToken", changeToken,
                        "message", "Código de verificação enviado para o novo email"
                );
            } else {
                // MFA não fornecido, retorna indicando que precisa
                return Map.of(
                        "success", true,
                        "requiresMfa", true,
                        "mfaRequired", true,
                        "message", "Verificação MFA necessária"
                );
            }
        } else {
            // Usuário não tem MFA, envia código de verificação para o novo email
            emailService.sendEmailChangeVerificationCode(request.newEmail(), verificationCode);
            
            return Map.of(
                    "success", true,
                    "requiresMfa", false,
                    "changeToken", changeToken,
                    "message", "Código de verificação enviado para o novo email"
            );
        }
    }
    
    private String generateVerificationCode() {
        return String.format("%06d", random.nextInt(1000000));
    }
    
    private void validateMfaCode(User user, String mfaCode) {
        if (user.getMfaType() == null || !Boolean.TRUE.equals(user.getMfaEnabled())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.MFA_NOT_ENABLED);
        }

        if (user.getMfaType() == MfaType.EMAIL_VERIFICATION) {
            if (!user.getMfaSecret().equals(mfaCode)) {
                throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
            }
        } else if (user.getMfaType() == MfaType.TOTP) {
            if (user.getMfaSecret() == null || otpService.isNotValid(user.getMfaSecret(), mfaCode)) {
                throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
            }
        } else {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
        }
    }
}