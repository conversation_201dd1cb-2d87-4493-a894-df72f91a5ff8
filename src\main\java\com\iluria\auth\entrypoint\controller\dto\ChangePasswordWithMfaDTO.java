package com.iluria.auth.entrypoint.controller.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

public record ChangePasswordWithMfaDTO(
    @NotNull(message = "Email é obrigatório")
    String email,
    
    @NotNull(message = "Senha atual é obrigatória")
    String currentPassword,
    
    @NotNull(message = "Nova senha é obrigatória")
    String newPassword,
    
    @NotNull(message = "Código MFA é obrigatório")
    @Size(min = 6, max = 6, message = "Código MFA deve ter 6 dígitos")
    String mfaCode
) {
}