package com.iluria.stores.store.service;

import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class SitePublishingProgressService {

    private final SimpMessagingTemplate messagingTemplate;

    public void sendProgress(String userEmail, ULID storeId, String step, String message, int progress) {
        SitePublishProgress progressData = SitePublishProgress.builder()
                .storeId(storeId.toString())
                .step(step)
                .message(message)
                .progress(progress)
                .timestamp(System.currentTimeMillis())
                .build();

        String destination = "/topic/site-publishing/" + userEmail;
        try {
            log.info("📤 Enviando progresso WebSocket: {} -> {} ({}%) para {}", destination, step, progress, userEmail);
            messagingTemplate.convertAndSend(destination, progressData);
            log.info("✅ Progresso enviado com sucesso: {}", progressData);
        } catch (Exception e) {
            log.error("❌ Erro ao enviar progresso WebSocket: {}", e.getMessage(), e);
        }
    }

    public void sendStarted(String userEmail, ULID storeId) {
        sendProgress(userEmail, storeId, "STARTED", "Iniciando publicação da loja...", 5);
    }

    public void sendBuildingSite(String userEmail, ULID storeId) {
        sendProgress(userEmail, storeId, "BUILDING_SITE", "Montando site...", 20);
    }

    public void sendDeployingAssets(String userEmail, ULID storeId) {
        sendProgress(userEmail, storeId, "DEPLOYING_ASSETS", "Implementando recursos...", 50);
    }

    public void sendFinalizing(String userEmail, ULID storeId) {
        sendProgress(userEmail, storeId, "FINALIZING", "Finalizando publicação...", 80);
    }

    public void sendCompleted(String userEmail, ULID storeId) {
        sendProgress(userEmail, storeId, "COMPLETED", "Loja publicada com sucesso!", 100);
    }

    public void sendError(String userEmail, ULID storeId, String error) {
        sendProgress(userEmail, storeId, "ERROR", "Error:" + error, -1);
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SitePublishProgress {
        private String storeId;
        private String step;
        private String message;
        private int progress;
        private long timestamp;
    }
}
