package com.iluria.auth.entrypoint.controller.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public record VerifyNewEmailDTO(
    @NotBlank(message = "Email atual é obrigatório")
    @Email(message = "Email inválido")
    String email,
    
    @NotBlank(message = "Novo email é obrigatório")
    @Email(message = "Email inválido")
    String newEmail,
    
    @NotBlank(message = "Código de verificação é obrigatório")
    String verificationCode,
    
    @NotBlank(message = "Token de alteração é obrigatório")
    String changeToken,
    
    // Campo opcional para preservar a sessão atual
    String currentSessionId
) {}