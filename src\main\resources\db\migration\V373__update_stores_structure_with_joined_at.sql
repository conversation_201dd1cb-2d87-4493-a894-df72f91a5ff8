-- Migration para atualizar a estrutura JSON do campo stores na tabela users
-- Adiciona joinedAt para cada store além do roleId
-- V373__update_stores_structure_with_joined_at.sql

-- Atualizar a estrutura do campo stores para incluir joinedAt
-- Converter de: {"storeId": "roleId"} 
-- Para: {"storeId": {"roleId": "roleId", "joinedAt": "timestamp"}}

UPDATE users 
SET stores = (
    SELECT JSON_OBJECTAGG(
        store_key,
        JSON_OBJECT(
            'roleId', store_value,
            'joinedAt', DATE_FORMAT(CURRENT_TIMESTAMP(6), '%Y-%m-%dT%H:%i:%s.%f')
        )
    )
    FROM (
        SELECT 
            JSON_UNQUOTE(JSON_EXTRACT(stores, CONCAT('$."', j.store_key, '"'))) as store_value,
            j.store_key
        FROM users u
        CROSS JOIN JSON_TABLE(
            JSON_KEYS(u.stores),
            '$[*]' COLUMNS (store_key VARCHAR(255) PATH '$')
        ) j
        WHERE u.id = users.id
    ) as store_data
)
WHERE stores IS NOT NULL 
  AND JSON_TYPE(stores) = 'OBJECT'
  AND JSON_LENGTH(stores) > 0
  -- Verificar se ainda não foi convertido (se o valor não é um objeto)
  AND NOT EXISTS (
      SELECT 1 
      FROM JSON_TABLE(
          JSON_KEYS(stores),
          '$[*]' COLUMNS (store_key VARCHAR(255) PATH '$')
      ) j
      WHERE JSON_TYPE(JSON_EXTRACT(stores, CONCAT('$."', j.store_key, '"'))) = 'OBJECT'
      LIMIT 1
  );

-- Comentário para documentação
ALTER TABLE users 
    MODIFY COLUMN stores JSON 
    COMMENT 'Estrutura JSON das lojas do usuário: {"storeId": {"roleId": "roleId", "joinedAt": "timestamp"}}';
