package com.iluria.auth.gateway;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.gateway.db.entity.EmailChangeRequestDBEntity;
import com.iluria.auth.gateway.db.mapper.EmailChangeRequestDBMapper;
import com.iluria.auth.gateway.db.repository.EmailChangeRequestRepository;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class EmailChangeRequestGateway {
    
    private final EmailChangeRequestRepository repository;
    private final EmailChangeRequestDBMapper mapper;
    
    public EmailChangeRequest save(EmailChangeRequest request) {
        EmailChangeRequestDBEntity entity = mapper.toDBEntity(request);
        EmailChangeRequestDBEntity saved = repository.save(entity);
        return mapper.toModel(saved);
    }
    
    public Optional<EmailChangeRequest> findByChangeToken(String token) {
        return repository.findByChangeToken(token)
                .map(mapper::toModel);
    }
    
    public Optional<EmailChangeRequest> findByUserIdAndStatus(ULID userId, EmailChangeStatus status) {
        return repository.findByUserIdAndStatus(userId.toBytes(), status)
                .map(mapper::toModel);
    }
    
    @Transactional
    public void invalidateOldRequests(ULID userId) {
        repository.invalidateOldRequests(userId.toBytes(), EmailChangeStatus.EXPIRED, EmailChangeStatus.PENDING);
    }
    
    public boolean existsPendingRequestForEmail(String newEmail) {
        return repository.existsByNewEmailAndStatus(newEmail, EmailChangeStatus.PENDING);
    }
}