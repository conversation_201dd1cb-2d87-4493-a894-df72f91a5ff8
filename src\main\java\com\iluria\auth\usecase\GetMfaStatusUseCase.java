package com.iluria.auth.usecase;

import org.springframework.stereotype.Service;

import com.iluria.auth.domain.MfaType;
import com.iluria.auth.domain.User;
import com.iluria.auth.gateway.UserGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GetMfaStatusUseCase {

    private final UserGateway userGateway;

    public MfaStatus getMfaStatus(ULID userId) {
        User user = userGateway.findById(userId);
        
        // MFA está habilitado se:
        // 1. Campo mfaEnabled é true E
        // 2. Tem um tipo de MFA definido
        boolean isEnabled = Boolean.TRUE.equals(user.getMfaEnabled()) && 
                           user.getMfaType() != null;
        
        return new MfaStatus(isEnabled, user.getMfaType());
    }

    public static class MfaStatus {
        private final boolean enabled;
        private final MfaType mfaType;
        
        public MfaStatus(boolean enabled, MfaType mfaType) {
            this.enabled = enabled;
            this.mfaType = mfaType;
        }
        
        public boolean isEnabled() { 
            return enabled; 
        }
        
        public MfaType getMfaType() { 
            return mfaType; 
        }
    }
}