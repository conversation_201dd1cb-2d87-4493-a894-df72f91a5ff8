package com.iluria.stores.store.service;

import org.springframework.stereotype.Service;
import com.iluria.commons.products.domain.Category;
import com.iluria.stores.products.domain.ProductReference;
import com.iluria.stores.store.gateway.CategoryReferenceGateway;
import com.iluria.stores.products.gateway.ProductReferenceGateway;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
public class StoreInitializationCacheService {

    private final CategoryReferenceGateway categoryReferenceGateway;
    private final ProductReferenceGateway productReferenceGateway;
    
    // Cache com TTL simples - limpa a cada 10 minutos
    private final Map<String, CacheEntry<List<Category>>> categoryCache = new ConcurrentHashMap<>();
    private final Map<String, CacheEntry<List<ProductReference>>> productCache = new ConcurrentHashMap<>();
    private static final long CACHE_TTL = 10 * 60 * 1000; // 10 minutos

    public List<Category> getCachedReferenceCategories(String language) {
        String cacheKey = "categories_" + language;
        CacheEntry<List<Category>> entry = categoryCache.get(cacheKey);
        
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        }
        
        // Cache miss ou expirado - buscar do banco
        List<Category> categories = categoryReferenceGateway.findAllHierarchicalByLanguage(language);
        categoryCache.put(cacheKey, new CacheEntry<>(categories));
        
        return categories;
    }

    public List<ProductReference> getCachedReferenceProducts(String language) {
        String cacheKey = "products_" + language;
        CacheEntry<List<ProductReference>> entry = productCache.get(cacheKey);
        
        if (entry != null && !entry.isExpired()) {
            return entry.getValue();
        }
        
        // Cache miss ou expirado - buscar do banco
        List<ProductReference> products = productReferenceGateway.findAllByLanguage(language);
        productCache.put(cacheKey, new CacheEntry<>(products));
        
        return products;
    }

    public void clearCache() {
        categoryCache.clear();
        productCache.clear();
    }

    private static class CacheEntry<T> {
        private final T value;
        private final long timestamp;

        public CacheEntry(T value) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
        }

        public T getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_TTL;
        }
    }
}