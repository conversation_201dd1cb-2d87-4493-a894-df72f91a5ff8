package com.iluria.auth.gateway.db.mapper;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.gateway.db.entity.EmailChangeRequestDBEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import io.github.jaspeen.ulid.ULID;

@Mapper(componentModel = "spring")
public interface EmailChangeRequestDBMapper {
    
    @Mapping(target = "id", expression = "java(toBytes(entity.getId()))")
    @Mapping(target = "userId", expression = "java(toBytes(entity.getUserId()))")
    EmailChangeRequestDBEntity toDBEntity(EmailChangeRequest entity);
    
    @Mapping(target = "id", expression = "java(toULID(dbEntity.getId()))")
    @Mapping(target = "userId", expression = "java(toULID(dbEntity.getUserId()))")
    EmailChangeRequest toModel(EmailChangeRequestDBEntity dbEntity);
    
    default byte[] toBytes(ULID ulid) {
        return ulid != null ? ulid.toBytes() : null;
    }
    
    default ULID toULID(byte[] bytes) {
        return bytes != null ? ULID.fromBytes(bytes) : null;
    }
}