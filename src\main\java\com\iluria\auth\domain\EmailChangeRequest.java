package com.iluria.auth.domain;

import io.github.jaspeen.ulid.ULID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailChangeRequest {
    private ULID id;
    private ULID userId;
    private String oldEmail;
    private String newEmail;
    private String verificationCode;
    private String changeToken;
    private LocalDateTime createdAt;
    private LocalDateTime expiresAt;
    private LocalDateTime usedAt;
    private EmailChangeStatus status;
    
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    public boolean isUsed() {
        return status == EmailChangeStatus.USED;
    }
    
    public boolean isValid() {
        return status == EmailChangeStatus.PENDING && !isExpired();
    }
}