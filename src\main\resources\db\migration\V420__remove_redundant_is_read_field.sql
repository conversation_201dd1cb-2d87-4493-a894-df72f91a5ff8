-- V420: Remove redundant is_read field from notifications table
-- The is_read status should only exist in user_notification_status table for individual user tracking
-- This simplifies the architecture and prevents confusion between shared and individual read states

-- Remove the redundant is_read and read_at columns from notifications table
-- The individual read status is properly managed in user_notification_status table
ALTER TABLE notifications 
DROP COLUMN is_read,
DROP COLUMN read_at;

-- Drop the user_notifications_view as it's not needed with proper architecture
-- Direct JOINs are clearer and more maintainable
DROP VIEW IF EXISTS user_notifications_view;

-- Update comments to clarify the proper architecture
ALTER TABLE notifications 
COMMENT = 'Base notification data. Read status is tracked per-user in user_notification_status table.';

ALTER TABLE user_notification_status 
COMMENT = 'Individual user read/delete status for notifications. This is the ONLY place where read status should be tracked.';
