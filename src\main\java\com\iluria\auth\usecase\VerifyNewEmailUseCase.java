package com.iluria.auth.usecase;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.domain.User;
import com.iluria.auth.entrypoint.controller.dto.VerifyNewEmailDTO;
import com.iluria.auth.exceptions.InvalidMfaTokenException;
import com.iluria.auth.gateway.EmailChangeRequestGateway;
import com.iluria.auth.gateway.UserGateway;
import com.iluria.auth.service.EmailService;
import com.iluria.auth.service.UserSessionService;
import com.iluria.exception.StoreErrorMessageEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class VerifyNewEmailUseCase {
    
    private final UserGateway userGateway;
    private final EmailChangeRequestGateway emailChangeRequestGateway;
    private final EmailService emailService;
    private final UserSessionService userSessionService;
    
    @Transactional
    public Map<String, Object> verifyAndChangeEmail(VerifyNewEmailDTO request, String currentSessionId) {
        // Busca a solicitação pelo token
        EmailChangeRequest changeRequest = emailChangeRequestGateway.findByChangeToken(request.changeToken())
                .orElseThrow(() -> new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_NOT_FOUND));
        
        // Valida se a solicitação não expirou
        if (changeRequest.isExpired()) {
            changeRequest.setStatus(EmailChangeStatus.EXPIRED);
            emailChangeRequestGateway.save(changeRequest);
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_EXPIRED);
        }
        
        // Valida se a solicitação não foi usada
        if (changeRequest.isUsed()) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_ALREADY_USED);
        }
        
        // Valida se os emails coincidem
        if (!changeRequest.getOldEmail().equals(request.email()) || 
            !changeRequest.getNewEmail().equals(request.newEmail())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_NOT_FOUND);
        }
        
        // Valida o código de verificação
        if (!changeRequest.getVerificationCode().equals(request.verificationCode())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_EMAIL_CHANGE_VERIFICATION_CODE);
        }
        
        // Busca o usuário
        User user = userGateway.findById(changeRequest.getUserId());
        
        // Valida se o email ainda não foi usado por outro usuário
        if (userGateway.verifyExistingUser(request.newEmail())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_ALREADY_EXISTS);
        }
        
        // Atualiza o email do usuário
        String oldEmail = user.getEmail();
        user.setEmail(request.newEmail());
        userGateway.save(user);
        
        // Marca a solicitação como usada
        changeRequest.setStatus(EmailChangeStatus.USED);
        changeRequest.setUsedAt(LocalDateTime.now());
        emailChangeRequestGateway.save(changeRequest);
        
        // Invalida todas as outras sessões do usuário (segurança), mantendo a atual
        try {
            int terminatedCount;
            if (currentSessionId != null && !currentSessionId.isEmpty()) {
                // Invalida apenas as outras sessões, mantém a atual
                terminatedCount = userSessionService.terminateAllOtherSessions(user.getId(), currentSessionId);
                log.info("Invalidadas {} outras sessões após troca de email para usuário {}, mantendo sessão atual {}", terminatedCount, user.getId(), currentSessionId);
            } else {
                // Se não tem sessionId, invalida todas (comportamento antigo como fallback)
                terminatedCount = userSessionService.terminateAllUserSessions(user.getId());
                log.info("Invalidadas {} sessões após troca de email para usuário {}", terminatedCount, user.getId());
            }
        } catch (Exception e) {
            log.error("Erro ao invalidar sessões após troca de email", e);
            // Não falha a operação se não conseguir invalidar sessões
        }
        
        // Envia emails de confirmação
        emailService.sendEmailChangeSuccess(oldEmail, request.newEmail());
        emailService.sendEmailChangeNotification(request.newEmail());
        
        return Map.of(
                "success", true,
                "message", "Email alterado com sucesso! Faça login novamente com seu novo email."
        );
    }
}