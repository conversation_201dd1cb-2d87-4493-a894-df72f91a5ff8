-- V389: Adicionar constraint de unicidade para file_manager
-- Corrige o problema de "Query did not return a unique result"

-- <PERSON><PERSON>, remover duplicatas existentes mantendo apenas o mais recente
DELETE fm1 FROM file_manager fm1
INNER JOIN file_manager fm2 
WHERE fm1.store_id = fm2.store_id 
  AND fm1.name = fm2.name 
  AND COALESCE(fm1.parent_folder_id, 0x00) = COALESCE(fm2.parent_folder_id, 0x00)
  AND fm1.environment = fm2.environment
  AND fm1.created_at < fm2.created_at;

-- Adicionar constraint de unicidade para evitar duplicatas futuras
ALTER TABLE file_manager 
ADD CONSTRAINT uk_file_manager_unique_file 
UNIQUE (store_id, name, parent_folder_id, environment);

-- <PERSON><PERSON>r índice para melhorar performance das consultas
CREATE INDEX idx_file_manager_unique_lookup 
ON file_manager(store_id, name, parent_folder_id, environment);
