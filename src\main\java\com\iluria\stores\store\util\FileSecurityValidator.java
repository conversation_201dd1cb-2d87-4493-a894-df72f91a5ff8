package com.iluria.stores.store.util;

import com.iluria.stores.store.exception.ThemeException;
import com.iluria.exception.StoreErrorMessageEnum;

import java.util.Set;
import java.util.regex.Pattern;

/**
 * Utility class for validating file names and paths to prevent security vulnerabilities.
 */
public class FileSecurityValidator {
    
    private static final Set<String> ALLOWED_EXTENSIONS = Set.of(".html", ".css", ".js", ".json", ".txt");
    private static final Pattern DANGEROUS_PATH_PATTERN = Pattern.compile("(\\.\\.|/|\\\\|:)");
    private static final Pattern VALID_FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\-\\.]+$");
    private static final int MAX_FILENAME_LENGTH = 255;
    private static final int MAX_PATH_LENGTH = 1000;
    
    /**
     * Validates and sanitizes a file name to prevent path traversal attacks.
     * 
     * @param fileName the file name to validate
     * @return the sanitized file name
     * @throws ThemeException if the file name is invalid
     */
    public static String validateAndSanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_FILE_NAME);
        }
        
        // Remove any leading/trailing whitespace
        fileName = fileName.trim();
        
        // Check length
        if (fileName.length() > MAX_FILENAME_LENGTH) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_FILE_NAME);
        }
        
        // Check for dangerous patterns
        if (DANGEROUS_PATH_PATTERN.matcher(fileName).find()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_FILE_NAME);
        }
        
        // Extract just the filename if path separators exist
        int lastSeparator = Math.max(fileName.lastIndexOf('/'), fileName.lastIndexOf('\\'));
        if (lastSeparator >= 0) {
            fileName = fileName.substring(lastSeparator + 1);
        }
        
        // Validate filename format
        if (!VALID_FILENAME_PATTERN.matcher(fileName).matches()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_FILE_NAME);
        }
        
        // Check extension
        if (!hasAllowedExtension(fileName)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_FILE_EXTENSION);
        }
        
        return fileName;
    }
    
    /**
     * Validates an S3 path to ensure it's safe and doesn't contain traversal attempts.
     * 
     * @param s3Path the S3 path to validate
     * @return the validated path
     * @throws ThemeException if the path is invalid
     */
    public static String validateS3Path(String s3Path) {
        if (s3Path == null || s3Path.trim().isEmpty()) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_PATH);
        }
        
        // Check length
        if (s3Path.length() > MAX_PATH_LENGTH) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_PATH);
        }
        
        // Check for dangerous patterns
        if (s3Path.contains("..") || s3Path.contains("//")) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_PATH);
        }
        
        // Ensure path doesn't start with slash
        if (s3Path.startsWith("/")) {
            s3Path = s3Path.substring(1);
        }
        
        return s3Path;
    }
    
    /**
     * Checks if a file has an allowed extension.
     * 
     * @param fileName the file name to check
     * @return true if the extension is allowed
     */
    private static boolean hasAllowedExtension(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        return ALLOWED_EXTENSIONS.stream().anyMatch(lowerFileName::endsWith);
    }
    
    /**
     * Validates that a theme template path is safe for the given store.
     * 
     * @param templatePath the template path
     * @param storeId the store ID
     * @return the validated path
     */
    public static String validateTemplatePath(String templatePath, String storeId) {
        if (templatePath == null || storeId == null) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_INVALID_PATH);
        }
        
        String validatedPath = validateS3Path(templatePath);
        
        // Ensure path contains the store ID to prevent cross-store access
        if (!validatedPath.contains(storeId)) {
            throw new ThemeException(StoreErrorMessageEnum.THEME_UNAUTHORIZED_ACCESS);
        }
        
        return validatedPath;
    }
}