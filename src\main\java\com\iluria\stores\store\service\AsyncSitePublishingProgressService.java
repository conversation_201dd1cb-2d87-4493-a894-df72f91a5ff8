package com.iluria.stores.store.service;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AsyncSitePublishingProgressService {

    private final SitePublishingProgressService progressService;

    @Async("sitePublishingExecutor")
    @Transactional
    public void publishSiteDataAsync(String userEmail, ULID storeId) {
        log.info("🚀 Iniciando publicação assíncrona para usuário: {} e loja: {}", userEmail, storeId);
        try {
            log.info("📋 Etapa 1: Enviando STARTED");
            progressService.sendStarted(userEmail, storeId);
            Thread.sleep(1500);

            log.info("📋 Etapa 2: Enviando BUILDING_SITE");
            progressService.sendBuildingSite(userEmail, storeId);
            Thread.sleep(1000);

            log.info("📋 Etapa 3: Enviando DEPLOYING_ASSETS");
            progressService.sendDeployingAssets(userEmail, storeId);
            Thread.sleep(2000);

            log.info("📋 Etapa 4: Enviando FINALIZING");
            progressService.sendFinalizing(userEmail, storeId);
            Thread.sleep(1000);

            log.info("📋 Etapa 5: Enviando COMPLETED");
            progressService.sendCompleted(userEmail, storeId);
            Thread.sleep(1500);
            log.info("✅ Publicação assíncrona concluída com sucesso");
        } catch (Exception e) {
            log.error("❌ Erro durante publicação assíncrona: {}", e.getMessage(), e);
            progressService.sendError(userEmail, storeId, e.getMessage());
        }
    }
}
