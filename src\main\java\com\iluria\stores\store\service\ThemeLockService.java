package com.iluria.stores.store.service;

import io.github.jaspeen.ulid.ULID;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

/**
 * Service to manage locks for theme operations to prevent race conditions.
 * Uses in-memory locks for single instance deployments.
 * For distributed deployments, this should be replaced with distributed locks (Redis, etc.)
 */
@Service
public class ThemeLockService {
    
    private final ConcurrentHashMap<String, ReentrantLock> storeLocks = new ConcurrentHashMap<>();
    private static final long LOCK_TIMEOUT_SECONDS = 30;
    
    /**
     * Acquire a lock for theme operations on a specific store.
     * 
     * @param storeId The store ID to lock
     * @return true if lock was acquired, false if timeout
     */
    public boolean acquireThemeOperationLock(ULID storeId) {
        String lockKey = getLockKey(storeId);
        ReentrantLock lock = storeLocks.computeIfAbsent(lockKey, k -> new ReentrantLock(true));
        
        try {
            return lock.tryLock(LOCK_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    /**
     * Release the lock for theme operations on a specific store.
     * 
     * @param storeId The store ID to unlock
     */
    public void releaseThemeOperationLock(ULID storeId) {
        String lockKey = getLockKey(storeId);
        ReentrantLock lock = storeLocks.get(lockKey);
        
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
    
    /**
     * Check if a theme operation is currently locked for a store.
     * 
     * @param storeId The store ID to check
     * @return true if locked, false otherwise
     */
    public boolean isThemeOperationLocked(ULID storeId) {
        String lockKey = getLockKey(storeId);
        ReentrantLock lock = storeLocks.get(lockKey);
        
        return lock != null && lock.isLocked();
    }
    
    /**
     * Get the number of threads waiting for the lock.
     * 
     * @param storeId The store ID to check
     * @return number of waiting threads
     */
    public int getQueueLength(ULID storeId) {
        String lockKey = getLockKey(storeId);
        ReentrantLock lock = storeLocks.get(lockKey);
        
        return lock != null ? lock.getQueueLength() : 0;
    }
    
    private String getLockKey(ULID storeId) {
        return "theme_lock_" + storeId.toString();
    }
    
    /**
     * Clean up locks that are no longer in use.
     * This should be called periodically to prevent memory leaks.
     */
    public void cleanupUnusedLocks() {
        storeLocks.entrySet().removeIf(entry -> {
            ReentrantLock lock = entry.getValue();
            return !lock.isLocked() && !lock.hasQueuedThreads();
        });
    }
}