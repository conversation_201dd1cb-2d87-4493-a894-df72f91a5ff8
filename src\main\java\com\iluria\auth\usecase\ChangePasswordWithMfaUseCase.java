package com.iluria.auth.usecase;

import com.iluria.auth.domain.MfaType;
import com.iluria.auth.domain.User;
import com.iluria.auth.entrypoint.controller.dto.ChangePasswordWithMfaDTO;
import com.iluria.auth.exceptions.InvalidMfaTokenException;
import com.iluria.auth.exceptions.PasswordRecoveryException;
import com.iluria.auth.gateway.UserGateway;
import com.iluria.auth.service.EmailService;
import com.iluria.auth.service.UserSessionService;
import com.iluria.auth.service.authentication.OtpService;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ChangePasswordWithMfaUseCase {

    private final UserGateway userGateway;
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService;
    private final OtpService otpService;
    private final UserSessionService userSessionService;

    @Transactional
    public void changePasswordWithMfa(ChangePasswordWithMfaDTO request, ULID userId, String currentSessionId) {
        if (request.email() == null || request.currentPassword() == null || 
            request.newPassword() == null || request.mfaCode() == null) {
            throw new PasswordRecoveryException(StoreErrorMessageEnum.INVALID_PASSWORD_CHANGE_DATA);
        }

        // Busca o usuário
        User user = userGateway.findByEmail(request.email());
        
        // Valida se o usuário corresponde ao token
        if (!user.getId().equals(userId)) {
            throw new PasswordRecoveryException(StoreErrorMessageEnum.INVALID_PASSWORD_CHANGE_DATA);
        }

        // Valida senha atual
        if (!passwordEncoder.matches(request.currentPassword(), user.getPassword())) {
            throw new PasswordRecoveryException(StoreErrorMessageEnum.INVALID_CURRENT_PASSWORD);
        }

        // Valida código MFA
        validateMfaCode(user, request.mfaCode());

        // Altera a senha
        String encryptedPassword = passwordEncoder.encode(request.newPassword());
        user.setPassword(encryptedPassword);
        userGateway.save(user);

        // Invalida todas as outras sessões (mantém apenas a atual)
        try {
            if (currentSessionId != null) {
                int terminatedCount = userSessionService.terminateAllOtherSessions(userId, currentSessionId);
                log.info("Invalidadas {} outras sessões após troca de senha para usuário {}", terminatedCount, userId);
            } else {
                log.warn("SessionId não fornecido para invalidação de sessões após troca de senha");
            }
        } catch (Exception e) {
            log.error("Erro ao invalidar sessões após troca de senha", e);
            // Não falha a operação se não conseguir invalidar sessões
        }

        // Envia email de confirmação
        emailService.sendPasswordChangeSuccess(request.email());
    }

    private void validateMfaCode(User user, String mfaCode) {
        if (user.getMfaType() == null || !Boolean.TRUE.equals(user.getMfaEnabled())) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.MFA_NOT_ENABLED);
        }

        if (user.getMfaType() == MfaType.EMAIL_VERIFICATION) {
            if (!user.getMfaSecret().equals(mfaCode)) {
                throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
            }
        } else if (user.getMfaType() == MfaType.TOTP) {
            if (user.getMfaSecret() == null || otpService.isNotValid(user.getMfaSecret(), mfaCode)) {
                throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
            }
        } else {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.INVALID_MFA_TOKEN);
        }
    }
}