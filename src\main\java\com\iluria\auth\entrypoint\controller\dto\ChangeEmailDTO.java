package com.iluria.auth.entrypoint.controller.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public record ChangeEmailDTO(
    @NotBlank(message = "Senha atual é obrigatória")
    String currentPassword,
    
    @NotBlank(message = "Novo email é obrigatório")
    @Email(message = "Email inválido")
    String newEmail,
    
    // Campo opcional para código MFA (quando usuário tem MFA habilitado)
    String mfaCode
) {}