package com.iluria.auth.usecase;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.exceptions.InvalidMfaTokenException;
import com.iluria.auth.gateway.EmailChangeRequestGateway;
import com.iluria.auth.service.EmailService;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Random;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResendEmailChangeVerificationUseCase {
    
    private final EmailChangeRequestGateway emailChangeRequestGateway;
    private final EmailService emailService;
    private final Random random = new Random();
    
    @Transactional
    public Map<String, Object> resendVerificationCode(ULID userId) {
        // Busca solicitação pendente do usuário
        EmailChangeRequest request = emailChangeRequestGateway
                .findByUserIdAndStatus(userId, EmailChangeStatus.PENDING)
                .orElseThrow(() -> new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_NOT_FOUND));
        
        // Verifica se a solicitação ainda é válida
        if (request.isExpired()) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_EXPIRED);
        }
        
        // Gera novo código de verificação
        String newVerificationCode = generateVerificationCode();
        
        // Atualiza a solicitação com o novo código
        request.setVerificationCode(newVerificationCode);
        emailChangeRequestGateway.save(request);
        
        // Envia o novo código para o email novo
        emailService.sendEmailChangeVerificationCode(request.getNewEmail(), newVerificationCode);
        
        return Map.of(
                "success", true,
                "message", "Novo código de verificação enviado com sucesso"
        );
    }
    
    private String generateVerificationCode() {
        return String.format("%06d", random.nextInt(1000000));
    }
}