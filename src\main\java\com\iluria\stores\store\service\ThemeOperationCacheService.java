package com.iluria.stores.store.service;

import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Smart caching service for theme operations
 * Provides intelligent caching with automatic invalidation
 */
@Service
@RequiredArgsConstructor
public class ThemeOperationCacheService {

    // In-memory cache for validation results (fast access)
    private final Map<String, ValidationCacheEntry> validationCache = new ConcurrentHashMap<>();
    
    // Cache TTL in minutes
    private static final int VALIDATION_CACHE_TTL_MINUTES = 5;
    private static final int TEMPLATE_CACHE_TTL_MINUTES = 30;

    /**
     * Cache template files for a theme
     * TTL: 30 minutes or until theme modification
     */
    @Cacheable(value = "theme-templates", key = "#themeId.toString()", 
               condition = "#result != null && #result.size() > 0")
    public Map<String, String> cacheTemplateFiles(ULID themeId, Map<String, String> templateFiles) {
        return templateFiles;
    }

    /**
     * Invalidate template cache when theme is modified
     */
    @CacheEvict(value = "theme-templates", key = "#themeId.toString()")
    public void invalidateTemplateCache(ULID themeId) {
        // Also clear validation cache for this store
        validationCache.entrySet().removeIf(entry -> 
            entry.getKey().contains(themeId.toString()));
    }

    /**
     * Invalidate all template caches for a store
     */
    @CacheEvict(value = "theme-templates", allEntries = true, 
                condition = "#storeId != null")
    public void invalidateAllTemplateCaches(ULID storeId) {
        // Clear validation cache for this store
        validationCache.entrySet().removeIf(entry -> 
            entry.getKey().startsWith(storeId.toString()));
    }

    /**
     * Smart validation cache - checks if files were recently validated
     */
    public boolean isRecentlyValidated(ULID storeId, int expectedFileCount) {
        String cacheKey = storeId.toString() + ":" + expectedFileCount;
        ValidationCacheEntry entry = validationCache.get(cacheKey);
        
        if (entry == null) {
            return false;
        }
        
        // Check if cache entry is still valid (within TTL)
        LocalDateTime expiryTime = entry.getTimestamp().plusMinutes(VALIDATION_CACHE_TTL_MINUTES);
        if (LocalDateTime.now().isAfter(expiryTime)) {
            validationCache.remove(cacheKey);
            return false;
        }
        
        return entry.isValid();
    }

    /**
     * Cache validation result
     */
    public void cacheValidationResult(ULID storeId, int expectedFileCount, boolean isValid) {
        String cacheKey = storeId.toString() + ":" + expectedFileCount;
        validationCache.put(cacheKey, new ValidationCacheEntry(isValid, LocalDateTime.now()));
    }

    /**
     * Cache backup operation result for faster subsequent operations
     */
    @Cacheable(value = "backup-operations", key = "#storeId.toString() + ':' + #operation", 
               condition = "#result == true")
    public boolean cacheBackupOperation(ULID storeId, String operation, boolean result) {
        return result;
    }

    /**
     * Invalidate backup operation cache
     */
    @CacheEvict(value = "backup-operations", key = "#storeId.toString() + ':' + #operation")
    public void invalidateBackupOperationCache(ULID storeId, String operation) {
        // Method intentionally empty - Spring handles the cache eviction
    }

    /**
     * Get cache statistics for monitoring
     */
    public CacheStats getCacheStats() {
        int validationCacheSize = validationCache.size();
        
        // Count expired entries
        LocalDateTime now = LocalDateTime.now();
        long expiredEntries = validationCache.values().stream()
            .mapToLong(entry -> {
                LocalDateTime expiryTime = entry.getTimestamp().plusMinutes(VALIDATION_CACHE_TTL_MINUTES);
                return now.isAfter(expiryTime) ? 1 : 0;
            })
            .sum();

        return new CacheStats(validationCacheSize, (int) expiredEntries);
    }

    /**
     * Cleanup expired cache entries (called periodically)
     */
    public void cleanupExpiredEntries() {
        LocalDateTime now = LocalDateTime.now();
        validationCache.entrySet().removeIf(entry -> {
            LocalDateTime expiryTime = entry.getValue().getTimestamp().plusMinutes(VALIDATION_CACHE_TTL_MINUTES);
            return now.isAfter(expiryTime);
        });
    }

    // Inner classes for cache entries

    private static class ValidationCacheEntry {
        private final boolean valid;
        private final LocalDateTime timestamp;

        public ValidationCacheEntry(boolean valid, LocalDateTime timestamp) {
            this.valid = valid;
            this.timestamp = timestamp;
        }

        public boolean isValid() {
            return valid;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }
    }

    public static class CacheStats {
        private final int validationCacheSize;
        private final int expiredEntries;

        public CacheStats(int validationCacheSize, int expiredEntries) {
            this.validationCacheSize = validationCacheSize;
            this.expiredEntries = expiredEntries;
        }

        public int getValidationCacheSize() {
            return validationCacheSize;
        }

        public int getExpiredEntries() {
            return expiredEntries;
        }
    }
}