package com.iluria.stores.store.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.iluria.stores.store.service.ThemeOperationCacheService;
import lombok.RequiredArgsConstructor;

/**
 * Cache configuration for theme operations
 * Enables intelligent caching with automatic cleanup
 */
@Configuration
@EnableCaching
@EnableScheduling
@RequiredArgsConstructor
public class CacheConfig {

    private final ThemeOperationCacheService cacheService;

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
            "theme-templates",      // Template files cache
            "backup-operations"     // Backup operation results cache
        );
    }

    /**
     * Scheduled task to cleanup expired cache entries
     * Runs every 10 minutes to prevent memory leaks
     */
    @Scheduled(fixedRate = 600000) // 10 minutes
    public void cleanupExpiredCacheEntries() {
        try {
            cacheService.cleanupExpiredEntries();
        } catch (Exception e) {
            // Don't let cache cleanup break the application
        }
    }
}