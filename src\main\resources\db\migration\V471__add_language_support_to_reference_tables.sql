-- Add language column to existing categories_ref table
ALTER TABLE categories_ref ADD COLUMN language VARCHAR(5) DEFAULT 'pt' NOT NULL;

-- Create index for language column
CREATE INDEX idx_categories_ref_language ON categories_ref(language);

-- Insert English versions of existing categories
INSERT INTO categories_ref (id, title, parent_id, description, position, language) VALUES
-- Root categories
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Clothing', NULL, 'Fashion clothing and accessories', 1, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Electronics', NULL, 'Electronic devices and technology', 2, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Home & Garden', NULL, 'Home and garden products', 3, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Sports & Recreation', NULL, 'Sports equipment and recreational items', 4, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Books & Media', NULL, 'Books, magazines and digital media', 5, 'en'),

-- Clothing subcategories
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'T-Shirts', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Men and women t-shirts', 1, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Pants', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Jeans, dress pants and casual pants', 2, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Dresses', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Women dresses for all occasions', 3, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Accessories', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Bags, belts and other accessories', 4, 'en'),

-- Electronics subcategories
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Smartphones', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Mobile phones and smartphones', 1, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Computers', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Laptops, desktops and tablets', 2, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Audio & Video', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Headphones, speakers and TV', 3, 'en'),

-- Home & Garden subcategories
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Furniture', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Home and office furniture', 1, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Decoration', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Home decorative items', 2, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Kitchen', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Kitchen utensils and appliances', 3, 'en'),

-- Sports & Recreation subcategories
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Fitness', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Gym and fitness equipment', 1, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Soccer', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Soccer equipment and clothing', 2, 'en'),
(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Swimming', UUID_TO_BIN('550e8400-e29b-41d4-a716-************'), 'Swimming equipment', 3, 'en');

-- Add language column to existing products_ref table
ALTER TABLE products_ref ADD COLUMN language VARCHAR(5) DEFAULT 'pt' NOT NULL;

-- Create index for language column
CREATE INDEX idx_products_ref_language ON products_ref(language);

-- Insert English versions of existing products
INSERT INTO products_ref (
    id, name, description, short_description, has_variation, type, status, 
    price, original_price, cost_price, stock_quantity_total, weight, 
    box_depth, box_length, box_width, highlight, supplier_name, supplier_link, 
    supplier_notes, meta_title, meta_description, url_slug, seo_attribute,
    variations, image_keys, default_category_ids, seo_variations, language
) VALUES

-- Product 1: Elegant Women's Skirt with color variations
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Elegant Women''s Skirt',
    'Elegant and versatile women''s skirt, perfect for various occasions. Made with high-quality fabric and available in several vibrant colors.',
    'Elegant and versatile women''s skirt',
    TRUE,
    'PHYSICAL',
    'ACTIVE',
    45.00,
    60.00,
    15.00,
    120,
    8,
    3,
    25,
    15,
    TRUE,
    'Women''s Fashion Ltd',
    'https://womensfashion.com',
    'Supplier specialized in quality women''s clothing',
    'Elegant Women''s Skirt',
    'Elegant and versatile women''s skirt, available in various colors',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Color',
    JSON_ARRAY(
        JSON_OBJECT(
            'id', NULL,
            'price', 45.00,
            'costPrice', 15.00,
            'stockQuantity', 25,
            'weight', 8,
            'attributes', JSON_OBJECT('color', 'Blue', 'size', 'M'),
            'imageKeys', JSON_ARRAY('skirtblue1.jpeg', 'skirtblue2.jpeg')
        ),
        JSON_OBJECT(
            'id', NULL,
            'price', 45.00,
            'costPrice', 15.00,
            'stockQuantity', 30,
            'weight', 8,
            'attributes', JSON_OBJECT('color', 'Green', 'size', 'M'),
            'imageKeys', JSON_ARRAY('skirtgreen.jpeg')
        ),
        JSON_OBJECT(
            'id', NULL,
            'price', 45.00,
            'costPrice', 15.00,
            'stockQuantity', 35,
            'weight', 8,
            'attributes', JSON_OBJECT('color', 'Red', 'size', 'M'),
            'imageKeys', JSON_ARRAY('skirtred1.jpeg', 'skirtred2.jpeg')
        )
    ),
    JSON_ARRAY('skirtblue1.jpeg', 'skirtblue2.jpeg', 'skirtgreen.jpeg', 'skirtred1.jpeg', 'skirtred2.jpeg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Clothing category ID
    JSON_ARRAY(
        JSON_OBJECT(
            'attribute', 'Color',
            'attributeValue', 'Blue',
            'metaTitle', 'Elegant Women''s Skirt Blue',
            'metaDescription', 'Elegant women''s skirt in blue, perfect for various occasions'
        ),
        JSON_OBJECT(
            'attribute', 'Color',
            'attributeValue', 'Green',
            'metaTitle', 'Elegant Women''s Skirt Green',
            'metaDescription', 'Elegant women''s skirt in green, versatile and modern'
        ),
        JSON_OBJECT(
            'attribute', 'Color',
            'attributeValue', 'Red',
            'metaTitle', 'Elegant Women''s Skirt Red',
            'metaDescription', 'Elegant women''s skirt in red, ideal for striking looks'
        )
    ),
    'en'
),

-- Product 2: Basic T-Shirt
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Basic T-Shirt',
    'High-quality cotton basic t-shirt, comfortable for daily use. Ideal for casual looks.',
    'Basic cotton t-shirt',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    49.90,
    59.90,
    25.00,
    100,
    200,
    5,
    30,
    20,
    TRUE,
    'Textile Brazil',
    'https://textilebrazil.com',
    'Reliable cotton fabric supplier. Delivery within 7 business days.',
    'Basic T-Shirt',
    'High-quality cotton basic t-shirt, comfortable for daily use.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('blouse.jpg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- T-Shirts category ID
    NULL,
    'en'
),

-- Product 3: Elegant Pink Dress
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Elegant Pink Dress',
    'Elegant pink dress, perfect for special occasions. Made with lightweight and comfortable fabric.',
    'Elegant pink dress',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    89.90,
    99.90,
    45.00,
    50,
    300,
    8,
    35,
    25,
    TRUE,
    'Women''s Fashion',
    'https://womensfashion.com',
    'Quality women''s fashion specialist',
    'Elegant Pink Dress',
    'Elegant pink dress, perfect for special occasions.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('pinkdress.jpg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Dresses category ID
    NULL,
    'en'
),

-- Product 4: High Heels
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'High Heel Shoes',
    'Elegant and comfortable women''s high heel shoes. Ideal for social and professional events.',
    'Elegant high heel shoes',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    129.90,
    149.90,
    65.00,
    30,
    400,
    12,
    25,
    15,
    FALSE,
    'Premium Shoes',
    'https://premiumshoes.com',
    'High-quality women''s footwear specialist',
    'High Heel Shoes',
    'Elegant and comfortable women''s high heel shoes.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('highheels.jpg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Accessories category ID
    NULL,
    'en'
),

-- Product 5: Midi Skirt
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Midi Skirt',
    'Versatile midi skirt, ideal for elegant feminine looks. High-quality fabric and perfect fit.',
    'Versatile midi skirt',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    69.90,
    79.90,
    35.00,
    60,
    250,
    6,
    32,
    22,
    FALSE,
    'Women''s Fashion',
    'https://womensfashion.com',
    'Quality women''s fashion specialist',
    'Midi Skirt',
    'Versatile midi skirt, ideal for elegant feminine looks.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('skirtblue1.jpeg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Clothing category ID
    NULL,
    'en'
),

-- Product 6: LED Ring Light
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'LED Ring Light',
    'Professional LED ring light for photography and video. Uniform and adjustable lighting, ideal for content creators.',
    'Professional LED ring light',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    199.90,
    249.90,
    100.00,
    25,
    800,
    15,
    40,
    40,
    TRUE,
    'Tech Pro',
    'https://techpro.com',
    'Content creator equipment specialist',
    'LED Ring Light',
    'Professional LED ring light for photography and video.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('ringlight.jpg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Audio & Video category ID
    NULL,
    'en'
),

-- Product 7: Wooden Hanger
(
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    'Wooden Hanger',
    'Premium wooden hanger, resistant and elegant. Ideal for organizing delicate clothes in the wardrobe.',
    'Premium wooden hanger',
    FALSE,
    'PHYSICAL',
    'ACTIVE',
    15.90,
    19.90,
    8.00,
    200,
    150,
    3,
    45,
    5,
    FALSE,
    'Organized Home',
    'https://organizedhome.com',
    'Home organization specialist',
    'Wooden Hanger',
    'Premium wooden hanger, resistant and elegant.',
    UUID_TO_BIN('550e8400-e29b-41d4-a716-************'),
    NULL,
    NULL,
    JSON_ARRAY('hanger.jpg'),
    JSON_ARRAY(BIN_TO_UUID(UUID_TO_BIN('550e8400-e29b-41d4-a716-************'))), -- Decoration category ID
    NULL,
    'en'
);