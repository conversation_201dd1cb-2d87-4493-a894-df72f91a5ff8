package com.iluria.auth.usecase;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.gateway.EmailChangeRequestGateway;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CheckPendingEmailChangeUseCase {
    
    private final EmailChangeRequestGateway emailChangeRequestGateway;
    
    public Map<String, Object> checkPendingEmailChange(ULID userId) {
        // Busca request pendente do usuário
        Optional<EmailChangeRequest> pendingRequest = emailChangeRequestGateway
                .findByUserIdAndStatus(userId, EmailChangeStatus.PENDING);
        
        if (pendingRequest.isEmpty()) {
            return Map.of(
                    "hasPendingRequest", false
            );
        }
        
        EmailChangeRequest request = pendingRequest.get();
        
        // Verifica se ainda não expirou
        if (request.getExpiresAt().isBefore(LocalDateTime.now())) {
            // Marca como expirada
            request.setStatus(EmailChangeStatus.EXPIRED);
            emailChangeRequestGateway.save(request);
            
            return Map.of(
                    "hasPendingRequest", false
            );
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("hasPendingRequest", true);
        response.put("oldEmail", request.getOldEmail());
        response.put("newEmail", request.getNewEmail());
        response.put("changeToken", request.getChangeToken());
        response.put("createdAt", request.getCreatedAt().toString());
        response.put("expiresAt", request.getExpiresAt().toString());
        
        return response;
    }
}