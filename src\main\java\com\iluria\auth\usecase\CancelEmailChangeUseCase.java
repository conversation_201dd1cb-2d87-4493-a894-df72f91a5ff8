package com.iluria.auth.usecase;

import com.iluria.auth.domain.EmailChangeRequest;
import com.iluria.auth.domain.EmailChangeStatus;
import com.iluria.auth.exceptions.InvalidMfaTokenException;
import com.iluria.auth.gateway.EmailChangeRequestGateway;
import com.iluria.exception.StoreErrorMessageEnum;
import io.github.jaspeen.ulid.ULID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class CancelEmailChangeUseCase {
    
    private final EmailChangeRequestGateway emailChangeRequestGateway;
    
    @Transactional
    public Map<String, Object> cancelEmailChange(ULID userId) {
        // Busca request pendente do usuário
        Optional<EmailChangeRequest> pendingRequest = emailChangeRequestGateway
                .findByUserIdAndStatus(userId, EmailChangeStatus.PENDING);
        
        if (pendingRequest.isEmpty()) {
            throw new InvalidMfaTokenException(StoreErrorMessageEnum.EMAIL_CHANGE_REQUEST_NOT_FOUND_FOR_CANCEL);
        }
        
        EmailChangeRequest request = pendingRequest.get();
        
        // Atualiza status para CANCELLED
        request.setStatus(EmailChangeStatus.CANCELLED);
        emailChangeRequestGateway.save(request);
        
        log.info("Email change request cancelled for user: {}", userId);
        
        return Map.of(
                "success", true,
                "message", "Solicitação de alteração de email cancelada com sucesso"
        );
    }
}