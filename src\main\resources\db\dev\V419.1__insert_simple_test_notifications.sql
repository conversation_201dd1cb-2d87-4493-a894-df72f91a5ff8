-- NOTE: This migration creates test notifications with generated BINARY(16) IDs for role permission testing
-- Each notification type that requires specific permissions is tested here
-- 
-- STORE NOTIFICATION PERMISSIONS MAPPING:
-- - LOW_STOCK_ALERTS: No specific permission (general store notification)
-- - NEW_SALES: Requires NOTIFICATION_NEW_SALES permission  
-- - PRODUCT_REVIEWS: Requires NOTIFICATION_PRODUCT_REVIEWS permission
-- - PRODUCT_QUESTIONS: Requires NOTIFICATION_PRODUCT_QUESTIONS permission
-- - NEWSLETTER_SUBSCRIPTIONS: Requires NOTIFICATION_NEWSLETTER_SUBSCRIPTIONS permission
-- - NEW_CUSTOMER_REGISTRATIONS: Requires NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS permission
-- - STORE_UPDATES: No specific permission (general store notification)

-- First, let's create test notifications for all permission-based types

-- 1. Test Store-wide notification (LOW_STOCK_ALERTS - no user_id)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('01E1E1E1E1E1E1E1E1E1E1E1E1E1E1E1') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'LOW_STOCK_ALERTS',
    'Produto com estoque baixo',
    'O produto "Camiseta Azul" está com apenas 3 unidades em estoque',
    '{"productName": "Camiseta Azul", "entityType": "product", "entityAction": "low_stock_alert", "extraData": {"currentStock": 3, "minStock": 10}}',
    0,
    '/products/stock',
    '2025-07-30 14:30:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 2. Test Store-wide notification (STORE_UPDATES - no user_id)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('02E2E2E2E2E2E2E2E2E2E2E2E2E2E2E2') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'STORE_UPDATES',
    'Loja atualizada com sucesso',
    'Sua loja foi atualizada com as novas configurações de pagamento',
    '{"updateType": "PAYMENT_CONFIG", "entityType": "store_configuration", "entityAction": "updated"}',
    0,
    '/settings/payments',
    '2025-07-30 14:20:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 3. Test User-specific notification (COLLAB_INVITES)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('03E3E3E3E3E3E3E3E3E3E3E3E3E3E3E3') as id,
    u.id as user_id,
    s.id as store_id,
    'COLLAB_INVITES',
    'Team invitation received',
    'You have been invited to join a team as Administrator',
    CONCAT('{"roleName": "Administrator", "entityType": "invitation", "inviteEmail": "', u.email, '", "entityAction": "sent"}'),
    0,
    '/user/settings?section=invites',
    '2025-07-30 12:13:15',
    NULL,
    0,
    NULL
FROM users u 
CROSS JOIN store s 
WHERE u.active = TRUE 
  AND s.id IS NOT NULL
LIMIT 1;

-- 4. Test Store notification (NEW_SALES - requires NOTIFICATION_NEW_SALES permission)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('04E4E4E4E4E4E4E4E4E4E4E4E4E4E4E4') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'NEW_SALES',
    'Nova venda realizada',
    'Produto ABC foi vendido por R$ 299,90',
    '{"productName": "Produto ABC", "orderTotal": 299.90, "customerName": "Cliente Teste", "entityType": "order", "entityAction": "sale_completed"}',
    0,
    '/orders',
    '2025-07-30 12:15:30',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 5. Test Store notification (PRODUCT_REVIEWS - requires NOTIFICATION_PRODUCT_REVIEWS permission)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('08E8E8E8E8E8E8E8E8E8E8E8E8E8E8E8') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'PRODUCT_REVIEWS',
    'Nova avaliação recebida',
    'O produto "Smartphone XYZ" recebeu uma avaliação de 5 estrelas',
    '{"productName": "Smartphone XYZ", "rating": 5, "customerName": "João Silva", "entityType": "product_review", "entityAction": "review_created"}',
    0,
    '/products/reviews',
    '2025-07-30 11:45:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 6. Test Store notification (PRODUCT_QUESTIONS - requires NOTIFICATION_PRODUCT_QUESTIONS permission)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('09E9E9E9E9E9E9E9E9E9E9E9E9E9E9E9') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'PRODUCT_QUESTIONS',
    'Nova pergunta sobre produto',
    'Cliente perguntou: "Este produto tem garantia de quantos anos?"',
    '{"productName": "Notebook Gamer", "customerName": "Maria Santos", "entityType": "product_question", "entityAction": "question_created", "extraData": {"question": "Este produto tem garantia de quantos anos?"}}',
    0,
    '/products/questions',
    '2025-07-30 10:30:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 7. Test Store notification (NEWSLETTER_SUBSCRIPTIONS - requires NOTIFICATION_NEWSLETTER_SUBSCRIPTIONS permission)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('10E1E1E1E1E1E1E1E1E1E1E1E1E1E1E1') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'NEWSLETTER_SUBSCRIPTIONS',
    'Nova inscrição na newsletter',
    '<EMAIL> se inscreveu na sua newsletter',
    '{"subscriberEmail": "<EMAIL>", "entityType": "newsletter_subscription", "entityAction": "subscription_created"}',
    0,
    '/marketing/newsletter',
    '2025-07-30 09:15:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 8. Test Store notification (NEW_CUSTOMER_REGISTRATIONS - requires NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS permission)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('11E1E1E1E1E1E1E1E1E1E1E1E1E1E1E1') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'NEW_CUSTOMER_REGISTRATIONS',
    'Novo cliente cadastrado',
    'Pedro Oliveira se cadastrou na sua loja',
    '{"customerName": "Pedro Oliveira", "customerEmail": "<EMAIL>", "entityType": "customer_registration", "entityAction": "registration_completed"}',
    0,
    '/customers',
    '2025-07-30 08:45:00',
    NULL,
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 9. Test additional NEW_SALES notification (read) to test filtering
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('12E1E1E1E1E1E1E1E1E1E1E1E1E1E1E1') as id,
    NULL as user_id, -- Store-wide notification
    s.id as store_id,
    'NEW_SALES',
    'Venda concluída',
    'Produto "Camiseta Vermelha" vendido por R$ 49,90',
    '{"productName": "Camiseta Vermelha", "orderTotal": 49.90, "customerName": "Ana Costa", "entityType": "order", "entityAction": "sale_completed"}',
    1, -- Already read
    '/orders',
    '2025-07-30 07:30:00',
    '2025-07-30 08:00:00', -- Read timestamp
    0,
    NULL
FROM store s 
WHERE s.id IS NOT NULL
LIMIT 1;

-- 10. Test Global notification (SYSTEM_UPDATES - NULL user_id and store_id for global visibility)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
VALUES (
    UNHEX('05E5E5E5E5E5E5E5E5E5E5E5E5E5E5E5'),
    NULL, -- Global notification - no specific user
    NULL, -- Global notification - no specific store
    'SYSTEM_UPDATES',
    'Nova atualização disponível',
    'Uma nova versão do sistema foi lançada com melhorias de performance',
    '{"entityType": "system", "entityAction": "update_released", "extraData": {"version": "2.1.0", "features": ["Performance improvements", "Bug fixes", "New notification system"], "releaseDate": "2025-07-30"}}',
    0, -- Not read by default
    '/system/updates',
    '2025-07-30 10:00:00',
    NULL, -- Not read yet
    0,
    NULL
);

-- 11. Test User-only notification (SECURITY_ALERTS - no store_id, only user_id)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('06E6E6E6E6E6E6E6E6E6E6E6E6E6E6E6') as id,
    u.id as user_id,
    NULL as store_id, -- User-only notification
    'SECURITY_ALERTS',
    'Nova tentativa de login detectada',
    'Foi detectada uma tentativa de login na sua conta de um novo dispositivo',
    '{"device": "Chrome no Windows", "location": "São Paulo, SP", "entityType": "security", "entityAction": "login_attempt"}',
    0,
    '/user/settings?section=security',
    '2025-07-30 13:00:00',
    NULL,
    0,
    NULL
FROM users u 
WHERE u.active = TRUE
LIMIT 1;

-- 12. Test User-only notification (ACCOUNT_CHANGES - no store_id, only user_id)
INSERT INTO notifications (
    id, 
    user_id, 
    store_id, 
    notification_type, 
    title, 
    content, 
    metadata, 
    is_read, 
    action_url, 
    created_at, 
    read_at, 
    is_globally_deleted, 
    globally_deleted_at
)
SELECT 
    UNHEX('07E7E7E7E7E7E7E7E7E7E7E7E7E7E7E7') as id,
    u.id as user_id,
    NULL as store_id, -- User-only notification  
    'ACCOUNT_CHANGES',
    'Perfil atualizado com sucesso',
    'Suas informações de perfil foram atualizadas',
    '{"changeType": "PROFILE_UPDATE", "entityType": "user_profile", "entityAction": "updated"}',
    0,
    '/user/settings',
    '2025-07-30 11:30:00',
    NULL,
    0,
    NULL
FROM users u 
WHERE u.active = TRUE
LIMIT 1;

-- Insert corresponding user_notification_status records for user-specific notifications
INSERT INTO user_notification_status (
    id,
    notification_id,
    user_id,
    is_read,
    read_at,
    is_deleted,
    deleted_at,
    created_at,
    updated_at
)
SELECT 
    CASE 
        WHEN n.id = UNHEX('03E3E3E3E3E3E3E3E3E3E3E3E3E3E3E3') THEN UNHEX('A3E3E3E3E3E3E3E3E3E3E3E3E3E3E3E3')
        WHEN n.id = UNHEX('04E4E4E4E4E4E4E4E4E4E4E4E4E4E4E4') THEN UNHEX('A4E4E4E4E4E4E4E4E4E4E4E4E4E4E4E4')
        WHEN n.id = UNHEX('06E6E6E6E6E6E6E6E6E6E6E6E6E6E6E6') THEN UNHEX('A6E6E6E6E6E6E6E6E6E6E6E6E6E6E6E6')
        WHEN n.id = UNHEX('07E7E7E7E7E7E7E7E7E7E7E7E7E7E7E7') THEN UNHEX('A7E7E7E7E7E7E7E7E7E7E7E7E7E7E7E7')
    END as id,
    n.id as notification_id,
    n.user_id,
    n.is_read,
    n.read_at,
    0 as is_deleted,
    NULL as deleted_at,
    n.created_at,
    n.created_at as updated_at
FROM notifications n
WHERE n.user_id IS NOT NULL -- Only create status for user-specific notifications
  AND n.id IN (
      UNHEX('03E3E3E3E3E3E3E3E3E3E3E3E3E3E3E3'), -- COLLAB_INVITES
      UNHEX('04E4E4E4E4E4E4E4E4E4E4E4E4E4E4E4'), -- NEW_SALES
      UNHEX('06E6E6E6E6E6E6E6E6E6E6E6E6E6E6E6'), -- SECURITY_ALERTS
      UNHEX('07E7E7E7E7E7E7E7E7E7E7E7E7E7E7E7')  -- ACCOUNT_CHANGES
  );

-- NOTE: 
-- - Global notifications (user_id = NULL, store_id = NULL) like SYSTEM_UPDATES don't need user_notification_status records - they are visible to all users automatically
-- - Store-wide notifications (user_id = NULL, store_id != NULL) like LOW_STOCK_ALERTS, NEW_SALES, PRODUCT_REVIEWS, etc. also don't need user_notification_status records - they are visible to store users automatically (subject to permission validation)
-- - Only user-specific notifications need user_notification_status records

-- SUMMARY OF TEST NOTIFICATIONS CREATED:
-- Store notifications (require specific permissions when implemented):
--   - LOW_STOCK_ALERTS (01): General store notification
--   - STORE_UPDATES (02): General store notification  
--   - NEW_SALES (04, 12): Requires NOTIFICATION_NEW_SALES permission
--   - PRODUCT_REVIEWS (08): Requires NOTIFICATION_PRODUCT_REVIEWS permission
--   - PRODUCT_QUESTIONS (09): Requires NOTIFICATION_PRODUCT_QUESTIONS permission
--   - NEWSLETTER_SUBSCRIPTIONS (10): Requires NOTIFICATION_NEWSLETTER_SUBSCRIPTIONS permission
--   - NEW_CUSTOMER_REGISTRATIONS (11): Requires NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS permission
-- User notifications:
--   - COLLAB_INVITES (03): Team invitation
--   - SECURITY_ALERTS (06): Security-related alerts
--   - ACCOUNT_CHANGES (07): Account profile changes
-- Global notifications:
--   - SYSTEM_UPDATES (05): System-wide updates